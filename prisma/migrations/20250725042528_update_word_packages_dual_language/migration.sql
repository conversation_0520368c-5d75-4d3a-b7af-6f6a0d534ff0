/*
  Warnings:

  - You are about to drop the column `language` on the `word_packages` table. All the data in the column will be lost.
  - Added the required column `source_language` to the `word_packages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `target_language` to the `word_packages` table without a default value. This is not possible if the table is not empty.

*/

-- Step 1: Add new columns with temporary default values
ALTER TABLE "word_packages"
ADD COLUMN "source_language" "Language",
ADD COLUMN "target_language" "Language";

-- Step 2: Migrate existing data
-- For English packages, set source_language=EN and target_language=VI (English words with Vietnamese explanations)
UPDATE "word_packages"
SET "source_language" = "language",
    "target_language" = CASE
        WHEN "language" = 'EN' THEN 'VI'::"Language"
        WHEN "language" = 'VI' THEN 'EN'::"Language"
        ELSE 'EN'::"Language"
    END
WHERE "language" IS NOT NULL;

-- Step 3: Make the new columns NOT NULL
ALTER TABLE "word_packages"
ALTER COLUMN "source_language" SET NOT NULL,
ALTER COLUMN "target_language" SET NOT NULL;

-- Step 4: Drop the old language column
ALTER TABLE "word_packages" DROP COLUMN "language";
