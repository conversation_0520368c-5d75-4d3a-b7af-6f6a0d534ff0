/*
  Warnings:

  - You are about to drop the column `word_id` on the `WordNetData` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[term,pos]` on the table `WordNetData` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `pos` to the `WordNetData` table without a default value. This is not possible if the table is not empty.
  - Added the required column `term` to the `WordNetData` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "WordNetData" DROP CONSTRAINT "WordNetData_word_id_fkey";

-- DropIndex
DROP INDEX "WordNetData_word_id_idx";

-- DropIndex
DROP INDEX "WordNetData_word_id_key";

-- AlterTable
ALTER TABLE "Word" ADD COLUMN     "wordNetDataId" TEXT;

-- AlterTable
ALTER TABLE "WordNetData" DROP COLUMN "word_id",
ADD COLUMN     "pos" TEXT NOT NULL,
ADD COLUMN     "term" TEXT NOT NULL;

-- CreateIndex
CREATE INDEX "WordNetData_term_idx" ON "WordNetData"("term");

-- CreateIndex
CREATE INDEX "WordNetData_pos_idx" ON "WordNetData"("pos");

-- CreateIndex
CREATE UNIQUE INDEX "WordNetData_term_pos_key" ON "WordNetData"("term", "pos");

-- AddForeignKey
ALTER TABLE "Word" ADD CONSTRAINT "Word_wordNetDataId_fkey" FOREIGN KEY ("wordNetDataId") REFERENCES "WordNetData"("id") ON DELETE SET NULL ON UPDATE CASCADE;
