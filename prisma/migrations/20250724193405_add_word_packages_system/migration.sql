-- CreateTable
CREATE TABLE "word_packages" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "language" "Language" NOT NULL,
    "difficulty" "Difficulty" NOT NULL,
    "category" TEXT NOT NULL,
    "tags" TEXT[],
    "word_count" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "word_packages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "word_package_words" (
    "id" TEXT NOT NULL,
    "word_package_id" TEXT NOT NULL,
    "term" TEXT NOT NULL,
    "language" "Language" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "word_package_words_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_word_packages" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "word_package_id" TEXT NOT NULL,
    "selected_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_word_packages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "word_package_words_word_package_id_term_language_key" ON "word_package_words"("word_package_id", "term", "language");

-- CreateIndex
CREATE UNIQUE INDEX "user_word_packages_user_id_word_package_id_key" ON "user_word_packages"("user_id", "word_package_id");

-- AddForeignKey
ALTER TABLE "word_package_words" ADD CONSTRAINT "word_package_words_word_package_id_fkey" FOREIGN KEY ("word_package_id") REFERENCES "word_packages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_word_packages" ADD CONSTRAINT "user_word_packages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_word_packages" ADD CONSTRAINT "user_word_packages_word_package_id_fkey" FOREIGN KEY ("word_package_id") REFERENCES "word_packages"("id") ON DELETE CASCADE ON UPDATE CASCADE;
