import { useState, useEffect } from 'react';
import { Language } from '@prisma/client';
import { WordNetData } from '@/models';

interface UseWordNetOptions {
	term: string;
	language?: Language;
	enabled?: boolean;
}

interface UseWordNetResult {
	wordNetData: WordNetData | null;
	isLoading: boolean;
	error: string | null;
}

export function useWordNet({ term, language = Language.EN, enabled = true }: UseWordNetOptions): UseWordNetResult {
	const [wordNetData, setWordNetData] = useState<WordNetData | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (!enabled || !term || language !== Language.EN) {
			setWordNetData(null);
			setIsLoading(false);
			setError(null);
			return;
		}

		const fetchWordNetData = async () => {
			setIsLoading(true);
			setError(null);

			try {
				const params = new URLSearchParams({
					language: language,
				});

				const response = await fetch(`/api/wordnet/${encodeURIComponent(term)}?${params}`);

				if (!response.ok) {
					throw new Error('Failed to fetch WordNet data');
				}

				const data = await response.json();
				
				// Check if we have any meaningful data
				const hasData = data.synsets?.length > 0 || 
								data.hypernyms?.length > 0 || 
								data.hyponyms?.length > 0 || 
								data.holonyms?.length > 0 || 
								data.meronyms?.length > 0 ||
								data.lemma;

				setWordNetData(hasData ? data : null);
			} catch (err) {
				console.error('Error fetching WordNet data:', err);
				setError(err instanceof Error ? err.message : 'Unknown error');
				setWordNetData(null);
			} finally {
				setIsLoading(false);
			}
		};

		fetchWordNetData();
	}, [term, language, enabled]);

	return {
		wordNetData,
		isLoading,
		error,
	};
}
