import { useCallback, useEffect, useRef, useState } from 'react';
import { keywordStorage, KeywordSyncAction } from '@/lib/keyword-storage';
import { KeywordWithDetail } from '@/models';

interface UseKeywordSyncOptions {
	onSyncSuccess?: (action: KeywordSyncAction) => void;
	onSyncError?: (action: KeywordSyncAction, error: Error) => void;
	syncInterval?: number; // milliseconds
	hideSuccessDelay?: number; // milliseconds to hide success indicator
}

/**
 * Hook for managing background synchronization of keywords
 */
export function useKeywordSync(options: UseKeywordSyncOptions = {}) {
	const {
		onSyncSuccess,
		onSyncError,
		syncInterval = 5000, // 5 seconds default
		hideSuccessDelay = 2000, // 2 seconds default
	} = options;

	const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const isSyncingRef = useRef(false);
	const hideSuccessTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const [showSyncSuccess, setShowSyncSuccess] = useState(false);

	/**
	 * Sync a single action with the server
	 */
	const syncAction = useCallback(
		async (action: KeywordSyncAction): Promise<boolean> => {
			try {
				let response: Response;

				switch (action.type) {
					case 'create':
						if (!action.data?.name) {
							throw new Error('Missing name for create action');
						}
						response = await fetch('/api/keywords', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ name: action.data.name }),
						});
						break;

					case 'update':
						if (!action.data?.keywordId || !action.data?.name) {
							throw new Error('Missing keywordId or name for update action');
						}
						response = await fetch(`/api/keywords/${action.data.keywordId}`, {
							method: 'PUT',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ name: action.data.name }),
						});
						break;

					case 'delete':
						if (!action.data?.keywordId) {
							throw new Error('Missing keywordId for delete action');
						}
						response = await fetch(`/api/keywords/${action.data.keywordId}`, {
							method: 'DELETE',
						});
						break;

					default:
						throw new Error(`Unknown action type: ${action.type}`);
				}

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || `Failed to ${action.type} keyword`);
				}

				// For create and update actions, we might want to update local data with server response
				if (action.type === 'create' || action.type === 'update') {
					const result = await response.json();
					// Update local storage with server data if needed
					if (action.type === 'create') {
						// Update the temporary keyword with the real server data
						const keywords = await keywordStorage.getKeywords();
						const updatedKeywords = keywords.map((keyword) =>
							keyword.id === action.id ? result : keyword
						);
						await keywordStorage.saveKeywords(updatedKeywords);
					}
				}

				onSyncSuccess?.(action);

				// Show success indicator briefly
				setShowSyncSuccess(true);
				if (hideSuccessTimeoutRef.current) {
					clearTimeout(hideSuccessTimeoutRef.current);
				}
				hideSuccessTimeoutRef.current = setTimeout(() => {
					setShowSyncSuccess(false);
				}, hideSuccessDelay);

				return true;
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Unknown sync error');
				onSyncError?.(action, err);
				return false;
			}
		},
		[onSyncSuccess, onSyncError, hideSuccessDelay]
	);

	/**
	 * Process the sync queue
	 */
	const processSyncQueue = useCallback(async () => {
		if (isSyncingRef.current) return;

		isSyncingRef.current = true;
		const queue = await keywordStorage.getSyncQueue();

		for (const action of queue) {
			const success = await syncAction(action);
			if (success) {
				await keywordStorage.removeFromSyncQueue(action.id);
			} else {
				// If sync fails, we keep the action in queue for retry
				// You might want to implement exponential backoff here
				break;
			}
		}

		isSyncingRef.current = false;
	}, [syncAction]);

	/**
	 * Start background sync
	 */
	const startSync = useCallback(() => {
		if (syncIntervalRef.current) return;

		// Initial sync
		processSyncQueue();

		// Set up interval
		syncIntervalRef.current = setInterval(() => {
			processSyncQueue();
		}, syncInterval);
	}, [processSyncQueue, syncInterval]);

	/**
	 * Stop background sync
	 */
	const stopSync = useCallback(() => {
		if (syncIntervalRef.current) {
			clearInterval(syncIntervalRef.current);
			syncIntervalRef.current = null;
		}
	}, []);

	/**
	 * Force sync now
	 */
	const syncNow = useCallback(async () => {
		await processSyncQueue();
	}, [processSyncQueue]);

	/**
	 * Fetch fresh keywords from server and update IndexedDB
	 */
	const fetchAndUpdateKeywords = useCallback(async (): Promise<KeywordWithDetail[]> => {
		try {
			const response = await fetch('/api/keywords');
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to fetch keywords');
			}
			const keywords = await response.json();
			await keywordStorage.saveKeywords(keywords);
			return keywords;
		} catch (error) {
			console.error('Failed to fetch keywords from server:', error);
			// Return local keywords as fallback
			return await keywordStorage.getKeywords();
		}
	}, []);

	/**
	 * Get sync queue status
	 */
	const getSyncStatus = useCallback(async () => {
		const queue = await keywordStorage.getSyncQueue();
		return {
			pendingActions: queue.length,
			isSync: isSyncingRef.current,
			hasUnsyncedChanges: queue.length > 0,
			showSyncSuccess,
		};
	}, [showSyncSuccess]);

	// Auto-start sync on mount and cleanup on unmount
	useEffect(() => {
		startSync();
		return () => {
			stopSync();
			if (hideSuccessTimeoutRef.current) {
				clearTimeout(hideSuccessTimeoutRef.current);
			}
		};
	}, [startSync, stopSync]);

	// Handle visibility change to sync when tab becomes visible
	useEffect(() => {
		const handleVisibilityChange = () => {
			if (!document.hidden) {
				syncNow();
			}
		};

		document.addEventListener('visibilitychange', handleVisibilityChange);
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
		};
	}, [syncNow]);

	return {
		startSync,
		stopSync,
		syncNow,
		fetchAndUpdateKeywords,
		getSyncStatus,
		isSync: isSyncingRef.current,
	};
}
