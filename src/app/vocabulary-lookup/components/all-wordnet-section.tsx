'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Loading<PERSON>pin<PERSON>,
	Translate,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { WordNetEntry } from '@/backend/services/wordnet.service';
import { Database, ChevronDown } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { WordNetCard } from './wordnet-card';

interface AllWordNetResponse {
	entries: WordNetEntry[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
	};
}

export function AllWordNetSection() {
	const { t } = useTranslation();
	const { showError } = useToast();

	const [entries, setEntries] = useState<WordNetEntry[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalCount, setTotalCount] = useState(0);
	const [hasNextPage, setHasNextPage] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasInitialLoad, setHasInitialLoad] = useState(false);

	const loadEntries = useCallback(
		async (page: number = 1, append: boolean = false) => {
			if (append) {
				setIsLoadingMore(true);
			} else {
				setIsLoading(true);
			}

			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: '20',
				});

				const response = await fetch(`/api/wordnet?${params}`);

				if (!response.ok) {
					throw new Error('Failed to load WordNet entries');
				}

				const data: AllWordNetResponse = await response.json();

				if (append) {
					setEntries((prev) => [...prev, ...data.entries]);
				} else {
					setEntries(data.entries);
				}

				setCurrentPage(data.pagination.page);
				setTotalCount(data.pagination.totalCount);
				setHasNextPage(data.pagination.hasNextPage);
				setHasInitialLoad(true);
			} catch (error) {
				console.error('Failed to load WordNet entries:', error);
				showError(t('vocabulary_lookup.search_error'));
			} finally {
				setIsLoading(false);
				setIsLoadingMore(false);
			}
		},
		[t, showError]
	);

	const loadMore = useCallback(async () => {
		if (hasNextPage && !isLoadingMore) {
			await loadEntries(currentPage + 1, true);
		}
	}, [hasNextPage, isLoadingMore, currentPage, loadEntries]);

	// Load initial data
	useEffect(() => {
		if (!hasInitialLoad) {
			loadEntries(1, false);
		}
	}, [loadEntries, hasInitialLoad]);

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Database className="h-5 w-5" />
					<Translate text="wordnet_lookup.all_entries" />
					{totalCount > 0 && (
						<span className="text-sm font-normal text-muted-foreground">
							({totalCount.toLocaleString()} entries)
						</span>
					)}
				</CardTitle>
			</CardHeader>
			<CardContent>
				{isLoading ? (
					<div className="flex items-center justify-center py-8">
						<LoadingSpinner className="h-8 w-8" />
						<span className="ml-2">
							<Translate text="vocabulary_lookup.loading" />
						</span>
					</div>
				) : entries.length > 0 ? (
					<div className="space-y-4">
						<div className="grid gap-4">
							{entries.map((entry) => (
								<WordNetCard key={`${entry.term}-${entry.pos}`} entry={entry} />
							))}
						</div>

						{/* Load More Button */}
						{hasNextPage && (
							<div className="flex justify-center pt-4">
								<Button
									variant="outline"
									onClick={loadMore}
									disabled={isLoadingMore}
									className="min-w-32"
								>
									{isLoadingMore ? (
										<>
											<LoadingSpinner className="h-4 w-4 mr-2" />
											<Translate text="vocabulary_lookup.loading_more" />
										</>
									) : (
										<>
											<ChevronDown className="h-4 w-4 mr-2" />
											<Translate text="vocabulary_lookup.load_more" />
										</>
									)}
								</Button>
							</div>
						)}

						{/* Entry Count */}
						<div className="text-center text-sm text-muted-foreground pt-2">
							<Translate
								text="vocabulary_lookup.entry_count"
								values={{
									count: entries.length.toLocaleString(),
									total: totalCount.toLocaleString(),
								}}
							/>
						</div>
					</div>
				) : (
					<div className="text-center py-8 text-muted-foreground">
						<Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>
							<Translate text="wordnet_lookup.no_entries" />
						</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
