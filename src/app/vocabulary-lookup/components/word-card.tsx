'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>nt,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>itle,
	Translate,
} from '@/components/ui';
import { WordNetInfo } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { WordDetail } from '@/models';
import { ChevronDown, ChevronUp, Volume2 } from 'lucide-react';
import { memo, useCallback, useState } from 'react';

interface WordCardProps {
	word: WordDetail;
	className?: string;
}

export const WordCard = memo(function WordCard({ word, className }: WordCardProps) {
	const [isExpanded, setIsExpanded] = useState(false);

	const toggleExpanded = useCallback(() => {
		setIsExpanded(!isExpanded);
	}, [isExpanded]);

	const handlePlayAudio = useCallback(() => {
		if (word.audio_url) {
			const audio = new Audio(word.audio_url);
			audio.play().catch(console.error);
		} else {
			// Use Web Speech API for pronunciation
			if ('speechSynthesis' in window) {
				const utterance = new SpeechSynthesisUtterance(word.term);
				utterance.lang = word.language === 'EN' ? 'en-US' : 'vi-VN';
				speechSynthesis.speak(utterance);
			}
		}
	}, [word.audio_url, word.term, word.language]);

	return (
		<Card className={cn('transition-all duration-200 hover:shadow-md', className)}>
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between">
					<div className="flex-1">
						<CardTitle className="flex items-center gap-3 text-xl">
							<span className="font-bold">{word.term}</span>
							<Badge variant="secondary" className="text-xs">
								<Translate text={getTranslationKeyOfLanguage(word.language)} />
							</Badge>
							{word.WordNetData && (
								<Badge variant="outline" className="text-xs">
									WordNet
								</Badge>
							)}
						</CardTitle>
						{word.definitions &&
							word.definitions.length > 0 &&
							word.definitions[0].ipa && (
								<div className="flex items-center gap-2 mt-2">
									<span className="text-sm text-muted-foreground font-mono">
										/{word.definitions[0].ipa}/
									</span>
									<Button
										variant="ghost"
										size="sm"
										onClick={handlePlayAudio}
										className="h-6 w-6 p-0"
									>
										<Volume2 className="h-3 w-3" />
									</Button>
								</div>
							)}
					</div>
					<Button variant="ghost" size="sm" onClick={toggleExpanded} className="ml-2">
						{isExpanded ? (
							<>
								<ChevronUp className="h-4 w-4 mr-1" />
								<Translate text="vocabulary_lookup.collapse_details" />
							</>
						) : (
							<>
								<ChevronDown className="h-4 w-4 mr-1" />
								<Translate text="vocabulary_lookup.expand_details" />
							</>
						)}
					</Button>
				</div>
			</CardHeader>

			<CardContent className="pt-0">
				{/* Basic Definitions (Always Visible) */}
				{word.definitions && word.definitions.length > 0 && (
					<div className="space-y-3">
						{word.definitions
							.slice(0, isExpanded ? undefined : 1)
							.map((definition, index) => (
								<div key={definition.id} className="space-y-2">
									{/* Parts of Speech */}
									{definition.pos && definition.pos.length > 0 && (
										<div className="flex flex-wrap gap-1">
											{definition.pos.map((pos, posIndex) => (
												<Badge
													key={posIndex}
													variant="outline"
													className="text-xs"
												>
													{pos}
												</Badge>
											))}
										</div>
									)}

									{/* Explanations */}
									{definition.explains && definition.explains.length > 0 && (
										<div className="space-y-1">
											<h4 className="text-sm font-medium">
												<Translate text="vocabulary_lookup.definitions" />
											</h4>
											{definition.explains.map((explain, explainIndex) => (
												<div key={explain.id} className="text-sm space-y-1">
													<div className="flex gap-2">
														<span className="font-medium">EN:</span>
														<span>{explain.EN}</span>
													</div>
													<div className="flex gap-2">
														<span className="font-medium">VI:</span>
														<span>{explain.VI}</span>
													</div>
												</div>
											))}
										</div>
									)}

									{/* Examples (Only when expanded) */}
									{isExpanded &&
										definition.examples &&
										definition.examples.length > 0 && (
											<div className="space-y-1">
												<h4 className="text-sm font-medium">
													<Translate text="vocabulary_lookup.examples" />
												</h4>
												{definition.examples.map(
													(example, exampleIndex) => (
														<div
															key={example.id}
															className="text-sm space-y-1 pl-4 border-l-2 border-muted"
														>
															<div className="flex gap-2">
																<span className="font-medium">
																	EN:
																</span>
																<span className="italic">
																	{example.EN}
																</span>
															</div>
															<div className="flex gap-2">
																<span className="font-medium">
																	VI:
																</span>
																<span className="italic">
																	{example.VI}
																</span>
															</div>
														</div>
													)
												)}
											</div>
										)}
								</div>
							))}
					</div>
				)}

				{/* WordNet Information (Only when expanded) */}
				{isExpanded && word.WordNetData && (
					<div className="mt-6 pt-4 border-t">
						<h4 className="text-sm font-medium mb-3">
							<Translate text="vocabulary_lookup.wordnet_info" />
						</h4>
						<WordNetInfo wordNetData={word.WordNetData} term={word.term} />
					</div>
				)}

				{/* No WordNet Data Message (Only when expanded and no data) */}
				{isExpanded && !word.WordNetData && (
					<div className="mt-6 pt-4 border-t">
						<div className="text-sm text-muted-foreground text-center py-4">
							<Translate text="vocabulary_lookup.no_wordnet_data" />
						</div>
					</div>
				)}

				{/* Show more indicator when collapsed */}
				{!isExpanded && word.definitions && word.definitions.length > 1 && (
					<div className="mt-3 text-xs text-muted-foreground">
						+{word.definitions.length - 1} more definition
						{word.definitions.length > 2 ? 's' : ''}
					</div>
				)}
			</CardContent>
		</Card>
	);
});
