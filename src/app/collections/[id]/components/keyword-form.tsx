'use client';

import { Button, Card, Input, Label, Translate } from '@/components/ui';
import { useKeywordsContext, useTranslation, useGuidance } from '@/contexts';
import { Plus, Sparkles, X, Check } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';

export function KeywordForm() {
	const { t } = useTranslation();
	const { markUserInteraction } = useGuidance();
	const {
		keywords,
		createKeyword,
		deleteKeyword,
		selectedKeywords,
		setSelectedKeywords,
		isLoading: isFetchingKeywords,
		syncStatus,
	} = useKeywordsContext();
	const [newKeyword, setNewKeyword] = useState('');

	const handleAddKeyword = useCallback(async () => {
		if (!newKeyword.trim()) return;

		const existingKeyword = keywords.find(
			(kw) => kw.content.toLowerCase() === newKeyword.toLowerCase()
		);

		if (existingKeyword) {
			if (!selectedKeywords.includes(existingKeyword.id))
				setSelectedKeywords([...selectedKeywords, existingKeyword.id]);
		} else {
			const createdKeyword = await createKeyword(newKeyword);
			if (createdKeyword) setSelectedKeywords([...selectedKeywords, createdKeyword.id]);
		}
		setNewKeyword('');
	}, [newKeyword, keywords, selectedKeywords, createKeyword, setSelectedKeywords]);

	const handleKeyPress = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement>) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				handleAddKeyword();
			}
		},
		[handleAddKeyword]
	);

	const handleRemoveKeyword = useCallback(
		(keywordId: string) => {
			setSelectedKeywords(selectedKeywords.filter((id) => id !== keywordId));
		},
		[selectedKeywords, setSelectedKeywords]
	);

	const filteredKeywords = useMemo(() => keywords, [keywords]);

	return (
		<div className="space-y-4">
			<Card className="p-7 rounded-2xl shadow-2xl bg-card">
				<div className="flex flex-col gap-3">
					<div className="flex items-center gap-2 mb-2">
						<Sparkles className="h-6 w-6 text-primary" />
						<Label className="text-lg font-bold text-primary tracking-tight">
							<Translate text="words.select_keywords" />
						</Label>
						{syncStatus.hasUnsyncedChanges && !syncStatus.showSyncSuccess && (
							<div className="ml-auto flex items-center gap-1 text-xs text-primary/60">
								<div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
								<span>Syncing...</span>
							</div>
						)}
						{syncStatus.showSyncSuccess && (
							<div className="ml-auto flex items-center gap-1 text-xs text-green-600">
								<Check className="w-3 h-3" />
								<span>Synced</span>
							</div>
						)}
					</div>

					<div className="flex gap-3 items-center">
						<div className="relative flex-1">
							<Input
								type="text"
								value={newKeyword}
								onChange={(e) => setNewKeyword(e.target.value)}
								onKeyDown={handleKeyPress}
								placeholder={t('words.keywords_placeholder')}
								className="h-11 py-2 text-sm rounded-xl focus:ring-2 focus:ring-primary/30 bg-background text-primary shadow-inner placeholder:text-primary/60"
								aria-label={t('words.keywords_placeholder')}
							/>
						</div>
						<Button
							onClick={handleAddKeyword}
							disabled={!newKeyword.trim()}
							size="sm"
							className="h-9 rounded-xl px-4 font-semibold bg-primary text-background shadow-lg hover:bg-primary/90 transition-all duration-200 flex gap-2 items-center text-sm"
						>
							<Plus className="h-4 w-4" />
							<Translate text="ui.add" />
						</Button>
					</div>

					<div className="space-y-3">
						<div className="min-h-[36px]">
							{!isFetchingKeywords && filteredKeywords.length > 0 ? (
								<div className="flex flex-wrap gap-3">
									{filteredKeywords.map((keyword) => {
										const isSelected = selectedKeywords.includes(keyword.id);
										return (
											<div key={keyword.id} className="relative">
												<span
													className={`inline-flex items-center px-4 py-1.5 rounded-xl font-medium text-sm select-none shadow-sm transition-all duration-200 cursor-pointer ${
														isSelected
															? 'bg-primary text-background shadow-lg'
															: 'outline bg-background text-primary'
													}`}
													onClick={() => {
														markUserInteraction();
														if (isSelected) {
															handleRemoveKeyword(keyword.id);
														} else {
															setSelectedKeywords([
																...selectedKeywords,
																keyword.id,
															]);
														}
													}}
												>
													{keyword.content}
												</span>
												<button
													onClick={(e) => {
														e.stopPropagation();
														deleteKeyword(keyword.id);
													}}
													className={`absolute -top-1.5 -right-1.5 flex items-center justify-center rounded-full transition-all duration-150 w-5 h-5 text-xs
													${
														isSelected
															? 'bg-background text-primary hover:bg-background/90'
															: 'bg-primary text-background hover:bg-primary/90'
													}
													shadow-sm hover:shadow-md focus:shadow-md
													opacity-90 hover:opacity-100 focus:opacity-100
												`}
													aria-label={`Delete ${keyword.content}`}
													tabIndex={0}
												>
													<X className="h-3 w-3" />
												</button>
											</div>
										);
									})}
								</div>
							) : !isFetchingKeywords && filteredKeywords.length === 0 ? (
								<div className="text-center py-3 text-primary/70 text-sm">
									<Translate text="words.no_keywords_found" />
								</div>
							) : null}
						</div>
					</div>
				</div>
			</Card>
		</div>
	);
}
