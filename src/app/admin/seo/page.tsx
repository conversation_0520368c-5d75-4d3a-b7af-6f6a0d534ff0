'use client';

import { AdminLayout } from '@/components/admin/admin-layout';
import { SeoManagementForm } from '@/components/admin/seo-management-form';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/contexts/toast-context';
import { SeoSettings } from '@prisma/client';
import { useEffect, useState } from 'react';

export default function AdminSeoPage() {
	const [settings, setSettings] = useState<SeoSettings | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSaving, setIsSaving] = useState(false);
	const { showError } = useToast();

	// Load SEO settings on mount
	useEffect(() => {
		const loadSettings = async () => {
			try {
				const response = await fetch('/api/admin/seo', {
					credentials: 'include',
				});
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to load SEO settings');
				}
				const data = await response.json();
				setSettings(data);
			} catch (error) {
				console.error('Failed to load SEO settings:', error);
				showError(error instanceof Error ? error.message : 'Failed to load SEO settings');
			} finally {
				setIsLoading(false);
			}
		};

		loadSettings();
	}, [showError]);

	const handleUpdate = (updatedSettings: SeoSettings) => {
		setSettings(updatedSettings);
	};

	if (isLoading) {
		return (
			<AdminLayout>
				<div className="flex items-center justify-center min-h-[400px]">
					<LoadingSpinner className="h-8 w-8" />
				</div>
			</AdminLayout>
		);
	}

	if (!settings) {
		return (
			<AdminLayout>
				<div className="text-center py-8">
					<h2 className="text-xl font-semibold text-gray-900 mb-2">
						Failed to Load SEO Settings
					</h2>
					<p className="text-gray-600 mb-4">
						There was an error loading the SEO settings. Please try refreshing the page.
					</p>
					<button
						onClick={() => window.location.reload()}
						className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
					>
						Refresh Page
					</button>
				</div>
			</AdminLayout>
		);
	}

	return (
		<AdminLayout>
			<div className="space-y-6">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">SEO Management</h1>
					<p className="text-gray-600 mt-1">
						Configure SEO settings, meta tags, and social media previews for your
						application.
					</p>
				</div>

				<SeoManagementForm
					settings={settings}
					onUpdate={handleUpdate}
					isLoading={isSaving}
				/>
			</div>
		</AdminLayout>
	);
}
