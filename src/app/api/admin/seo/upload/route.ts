import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// Allowed file types for uploads
const ALLOWED_IMAGE_TYPES = [
	'image/jpeg',
	'image/jpg',
	'image/png',
	'image/gif',
	'image/webp',
	'image/svg+xml',
];
const ALLOWED_FAVICON_TYPES = [
	'image/x-icon',
	'image/vnd.microsoft.icon',
	'image/ico',
	...ALLOWED_IMAGE_TYPES,
];

// Maximum file sizes (in bytes)
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FAVICON_SIZE = 1 * 1024 * 1024; // 1MB

/**
 * POST /api/admin/seo/upload
 * Upload favicon or logo files
 */
async function handlePost(request: NextRequest) {
	try {
		const formData = await request.formData();
		const file = formData.get('file') as File;
		const type = formData.get('type') as string; // 'favicon' or 'logo'

		if (!file) {
			return NextResponse.json({ error: 'No file provided' }, { status: 400 });
		}

		if (!type || !['favicon', 'logo'].includes(type)) {
			return NextResponse.json(
				{ error: 'Invalid upload type. Must be "favicon" or "logo"' },
				{ status: 400 }
			);
		}

		// Validate file type
		const allowedTypes = type === 'favicon' ? ALLOWED_FAVICON_TYPES : ALLOWED_IMAGE_TYPES;
		if (!allowedTypes.includes(file.type)) {
			return NextResponse.json(
				{
					error: `Invalid file type. Allowed types for ${type}: ${allowedTypes.join(
						', '
					)}`,
				},
				{ status: 400 }
			);
		}

		// Validate file size
		const maxSize = type === 'favicon' ? MAX_FAVICON_SIZE : MAX_IMAGE_SIZE;
		if (file.size > maxSize) {
			return NextResponse.json(
				{ error: `File too large. Maximum size for ${type}: ${maxSize / (1024 * 1024)}MB` },
				{ status: 400 }
			);
		}

		// Generate unique filename
		const timestamp = Date.now();
		const originalName = file.name;
		const extension = originalName.split('.').pop();
		const filename = `${type}-${timestamp}.${extension}`;

		// Create upload directory if it doesn't exist
		const uploadDir = join(process.cwd(), 'public', 'uploads', 'seo');
		if (!existsSync(uploadDir)) {
			await mkdir(uploadDir, { recursive: true });
		}

		// Save file
		const filePath = join(uploadDir, filename);
		const bytes = await file.arrayBuffer();
		const buffer = Buffer.from(bytes);
		await writeFile(filePath, buffer);

		// Return the public URL
		const publicUrl = `/uploads/seo/${filename}`;

		return NextResponse.json({
			success: true,
			url: publicUrl,
			filename,
			type,
			size: file.size,
			originalName,
		});
	} catch (error) {
		console.error('Failed to upload file:', error);
		throw new Error('Failed to upload file');
	}
}

/**
 * DELETE /api/admin/seo/upload
 * Delete uploaded files (cleanup)
 */
async function handleDelete(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const filename = searchParams.get('filename');

		if (!filename) {
			return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
		}

		// Validate filename to prevent path traversal
		if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
			return NextResponse.json({ error: 'Invalid filename' }, { status: 400 });
		}

		// Delete file
		const filePath = join(process.cwd(), 'public', 'uploads', 'seo', filename);

		try {
			const fs = await import('fs/promises');
			await fs.unlink(filePath);
		} catch (error) {
			// File might not exist, which is fine
			console.warn('File not found for deletion:', filename);
		}

		return NextResponse.json({
			success: true,
			message: 'File deleted successfully',
		});
	} catch (error) {
		console.error('Failed to delete file:', error);
		throw new Error('Failed to delete file');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));

export const DELETE = withAdminAuth(withErrorHandling(handleDelete));
