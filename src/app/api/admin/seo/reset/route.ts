import { getSeoService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { clearSeoCache } from '@/lib/seo';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/admin/seo/reset
 * Reset SEO settings to defaults
 */
async function handlePost(request: NextRequest) {
	try {
		const seoService = getSeoService();

		// Reset to defaults
		const defaultSettings = await seoService.resetToDefaults();

		// Clear SEO cache to ensure fresh data on next request
		clearSeoCache();

		return NextResponse.json(defaultSettings);
	} catch (error) {
		console.error('Failed to reset SEO settings:', error);
		throw new Error('Failed to reset SEO settings');
	}
}

// Apply middleware and export handler
export const POST = withAdminAuth(withErrorHandling(handlePost));
