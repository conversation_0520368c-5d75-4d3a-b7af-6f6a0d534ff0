import { ValidationError } from '@/backend/errors';
import { getWordNetService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';

async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const pageParam = searchParams.get('page');
		const limitParam = searchParams.get('limit');

		// Validate and set pagination parameters
		const page = pageParam ? parseInt(pageParam, 10) : 1;
		const limit = limitParam ? parseInt(limitParam, 10) : 20;

		if (isNaN(page) || page < 1) {
			throw new ValidationError('Page must be a positive number.');
		}

		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordNetService = getWordNetService();
		const result = await wordNetService.getAllWordNetEntries(page, limit);

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to get WordNet entries:', error);
		return NextResponse.json(
			{ error: 'Failed to get WordNet entries. Please try again.' },
			{ status: 500 }
		);
	}
}

const wrappedGET = withErrorHandling(GET);
export { wrappedGET as GET };
