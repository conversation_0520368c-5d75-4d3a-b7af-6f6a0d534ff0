import { ValidationError } from '@/backend/errors';
import { getWordNetService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';

async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const term = searchParams.get('term');
		const limitParam = searchParams.get('limit');

		// Validate required parameters
		if (!term || term.trim() === '') {
			throw new ValidationError('Search term is required and cannot be empty.');
		}

		// Validate and set limit
		const limit = limitParam ? parseInt(limitParam, 10) : 20;
		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordNetService = getWordNetService();
		const wordNetEntries = await wordNetService.searchWordNetEntries(term.trim(), limit);

		return NextResponse.json(wordNetEntries);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to search WordNet entries:', error);
		return NextResponse.json(
			{ error: 'Failed to search WordNet entries. Please try again.' },
			{ status: 500 }
		);
	}
}

const wrappedGET = withErrorHandling(GET);
export { wrappedGET as GET };
