import { NextRequest, NextResponse } from 'next/server';
import { getWordPackageService } from '@/backend/wire';
import { withAuth, withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { Language, Difficulty } from '@prisma/client';
import { z } from 'zod';

const getWordPackagesSchema = z.object({
	source_language: z.nativeEnum(Language).nullable().optional(),
	target_language: z.nativeEnum(Language).nullable().optional(),
	difficulty: z.nativeEnum(Difficulty).nullable().optional(),
	category: z.string().nullable().optional(),
	search: z.string().nullable().optional(),
	limit: z.coerce.number().min(1).max(50).default(20),
});

/**
 * GET /api/word-packages
 * Get available word packages for user
 */
async function handleGet(request: NextRequest): Promise<NextResponse> {
	const { searchParams } = new URL(request.url);
	const userId = request.headers.get('x-user-id');

	if (!userId) {
		return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
	}

	try {
		const params = getWordPackagesSchema.parse({
			source_language: searchParams.get('source_language'),
			target_language: searchParams.get('target_language'),
			difficulty: searchParams.get('difficulty'),
			category: searchParams.get('category'),
			search: searchParams.get('search'),
			limit: searchParams.get('limit'),
		});

		const wordPackageService = getWordPackageService();
		const packages = await wordPackageService.getAvailablePackages(
			userId,
			{
				source_language: params.source_language || undefined,
				target_language: params.target_language || undefined,
				difficulty: params.difficulty || undefined,
				category: params.category || undefined,
				search: params.search || undefined,
			},
			params.limit
		);

		return NextResponse.json(packages);
	} catch (error) {
		console.error('Failed to get word packages:', error);
		throw new Error('Failed to get word packages');
	}
}

const createWordPackageSchema = z.object({
	name: z.string().min(1, 'Name is required'),
	description: z.string().min(1, 'Description is required'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	category: z.string().min(1, 'Category is required'),
	tags: z.array(z.string()).default([]),
	words: z
		.array(
			z.object({
				term: z.string().min(1),
				language: z.nativeEnum(Language),
			})
		)
		.min(1, 'At least one word is required'),
});

/**
 * POST /api/word-packages
 * Create a new word package (admin only)
 */
async function handlePost(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const data = createWordPackageSchema.parse(body);

		const wordPackageService = getWordPackageService();
		const wordPackage = await wordPackageService.createPackage(data);

		return NextResponse.json(wordPackage, { status: 201 });
	} catch (error) {
		console.error('Failed to create word package:', error);
		throw new Error('Failed to create word package');
	}
}

// Apply middleware and export handlers
export const GET = withAuth(withErrorHandling(handleGet));
export const POST = withAdminAuth(withErrorHandling(handlePost));
