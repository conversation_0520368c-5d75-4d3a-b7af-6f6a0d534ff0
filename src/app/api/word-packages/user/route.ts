import { NextRequest, NextResponse } from 'next/server';
import { getWordPackageService } from '@/backend/wire';
import { withAuth, withErrorHandling } from '@/lib/api-error-middleware';
import { Language } from '@prisma/client';
import { z } from 'zod';

const getUserPackagesSchema = z.object({
	limit: z.coerce.number().min(1).max(100).default(50),
	source_language: z.nativeEnum(Language).nullable().optional(),
	target_language: z.nativeEnum(Language).nullable().optional(),
});

/**
 * GET /api/word-packages/user
 * Get word packages selected by the current user
 * Optional language filters to show only packages matching collection source and target languages
 */
async function handleGet(request: NextRequest): Promise<NextResponse> {
	const { searchParams } = new URL(request.url);
	const userId = request.headers.get('x-user-id');

	if (!userId) {
		return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
	}

	try {
		const params = getUserPackagesSchema.parse({
			limit: searchParams.get('limit'),
			source_language: searchParams.get('source_language'),
			target_language: searchParams.get('target_language'),
		});

		const wordPackageService = getWordPackageService();
		const userPackages = await wordPackageService.getUserPackages(
			userId,
			params.limit,
			params.source_language || undefined,
			params.target_language || undefined
		);

		return NextResponse.json(userPackages);
	} catch (error) {
		console.error('Failed to get user word packages:', error);
		throw new Error('Failed to get user word packages');
	}
}

// Apply middleware and export handler
export const GET = withAuth(withErrorHandling(handleGet));
