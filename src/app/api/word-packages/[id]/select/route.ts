import { NextRequest, NextResponse } from 'next/server';
import { getWordPackageService } from '@/backend/wire';
import { withAuth, withErrorHandling } from '@/lib/api-error-middleware';
import { z } from 'zod';

const selectPackageSchema = z.object({
	collectionId: z.string().min(1, 'Collection ID is required'),
});

/**
 * POST /api/word-packages/[id]/select
 * Select a word package and add its words to user's collection
 */
async function handlePost(
	request: NextRequest,
	{ params }: { params: { id: string } }
): Promise<NextResponse> {
	const packageId = params.id;
	const userId = request.headers.get('x-user-id');

	if (!userId) {
		return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
	}

	if (!packageId) {
		return NextResponse.json({ error: 'Package ID is required' }, { status: 400 });
	}

	try {
		const body = await request.json();
		const { collectionId } = selectPackageSchema.parse(body);

		const wordPackageService = getWordPackageService();
		
		// Check if user already selected this package
		const alreadySelected = await wordPackageService.hasUserSelectedPackage(userId, packageId);
		if (alreadySelected) {
			return NextResponse.json(
				{ error: 'You have already selected this word package' },
				{ status: 409 }
			);
		}

		// Select the package and add words to collection
		await wordPackageService.selectPackageForUser(userId, packageId, collectionId);

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error('Failed to select word package:', error);
		if (error instanceof Error) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}
		throw new Error('Failed to select word package');
	}
}

// Apply middleware and export handler
export const POST = withAuth(withErrorHandling(handlePost));
