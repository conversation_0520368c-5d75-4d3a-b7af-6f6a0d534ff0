import { ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const pageParam = searchParams.get('page');
		const limitParam = searchParams.get('limit');

		// Validate and set pagination parameters
		const page = pageParam ? parseInt(pageParam, 10) : 1;
		const limit = limitParam ? parseInt(limitParam, 10) : 50;

		if (isNaN(page) || page < 1) {
			throw new ValidationError('Page must be a positive number.');
		}

		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordService = getWordService();

		// Calculate total limit including pagination
		const totalLimit = page * limit;

		// Get words using searchWords method with empty term to get all words
		// This method already prioritizes words with WordNet data
		const allWords = await wordService.searchWords('', undefined, totalLimit);

		// Apply pagination by slicing
		const startIndex = (page - 1) * limit;
		const endIndex = startIndex + limit;
		const paginatedWords = allWords.slice(startIndex, endIndex);

		// For total count, get all words without limit
		const allWordsForCount = await wordService.searchWords('', undefined, undefined);
		const totalCount = allWordsForCount.length;
		const totalPages = Math.ceil(totalCount / limit);

		const response = {
			words: paginatedWords,
			pagination: {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1,
			},
		};

		return NextResponse.json(response);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to fetch words:', error);
		return NextResponse.json(
			{ error: 'Failed to fetch words. Please try again.' },
			{ status: 500 }
		);
	}
}
