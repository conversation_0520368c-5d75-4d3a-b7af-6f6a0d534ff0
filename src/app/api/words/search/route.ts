import { ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const term = searchParams.get('term');
		const limitParam = searchParams.get('limit');

		// Validate required parameters
		if (!term || term.trim() === '') {
			throw new ValidationError('Search term is required and cannot be empty.');
		}

		// Validate and set limit
		const limit = limitParam ? parseInt(limitParam, 10) : 20;
		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordService = getWordService();
		const words = await wordService.searchWords(term.trim(), undefined, limit);

		return NextResponse.json(words);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to search words:', error);
		return NextResponse.json(
			{ error: 'Failed to search words. Please try again.' },
			{ status: 500 }
		);
	}
}
