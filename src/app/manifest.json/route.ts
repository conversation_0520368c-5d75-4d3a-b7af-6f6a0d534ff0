import { updateManifest } from '@/lib/seo';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const manifest = await updateManifest();
		
		return NextResponse.json(manifest, {
			headers: {
				'Content-Type': 'application/manifest+json',
				'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
			},
		});
	} catch (error) {
		console.error('Failed to generate manifest:', error);
		
		// Return default manifest on error
		const defaultManifest = {
			name: 'Vocab Learning App',
			short_name: 'Vocab',
			description: 'Learn new vocabulary with AI assistance',
			start_url: '/',
			display: 'standalone',
			background_color: '#ffffff',
			theme_color: '#000000',
			orientation: 'portrait',
			scope: '/',
			dir: 'ltr',
			lang: 'en',
			categories: ['education', 'productivity'],
			icons: [
				{
					src: '/icon-192x192.ico',
					sizes: '192x192',
					type: 'image/ico',
					purpose: 'any maskable'
				},
				{
					src: '/icon-512x512.ico',
					sizes: '512x512',
					type: 'image/ico',
					purpose: 'any maskable'
				}
			]
		};
		
		return NextResponse.json(defaultManifest, {
			headers: {
				'Content-Type': 'application/manifest+json',
			},
		});
	}
}
