import { KeywordWithDetail } from '@/models';
import { DB_NAME, DB_VERSION, IndexedDBManager } from './indexed-db';

const KEYWORDS_STORE_NAME = 'keywords';
const SELECTED_KEYWORDS_STORE_NAME = 'selected-keywords';
const KEYWORD_SYNC_QUEUE_STORE_NAME = 'keyword-sync-queue';

export interface KeywordSyncAction {
	id: string;
	type: 'create' | 'update' | 'delete';
	data?: {
		name?: string;
		keywordId?: string;
	};
	timestamp: number;
}

export interface KeywordStorageData {
	keywords: KeywordWithDetail[];
	lastSyncTimestamp: number;
	version: number;
}

export interface SelectedKeywordsData {
	selectedIds: string[];
	lastUpdated: number;
}

// Instantiate the manager for keyword-specific stores
const dbManager = new IndexedDBManager(DB_NAME, DB_VERSION, [
	{ storeName: KEYWORDS_STORE_NAME, options: { keyPath: 'id' } },
	{ storeName: SELECTED_KEYWORDS_STORE_NAME, options: { keyPath: 'id' } },
	{ storeName: KEYWORD_SYNC_QUEUE_STORE_NAME, options: { keyPath: 'id' } },
]);

/**
 * Save a single keyword to IndexedDB
 */
export async function saveKeyword(keyword: KeywordWithDetail): Promise<void> {
	return dbManager.put(KEYWORDS_STORE_NAME, keyword);
}

/**
 * Save multiple keywords to IndexedDB in a single transaction
 */
export async function saveKeywords(keywords: KeywordWithDetail[]): Promise<void> {
	const db = await dbManager.getDB();
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([KEYWORDS_STORE_NAME], 'readwrite');
		const store = transaction.objectStore(KEYWORDS_STORE_NAME);

		keywords.forEach((keyword) => {
			store.put(keyword);
		});

		transaction.oncomplete = () => resolve();
		transaction.onerror = (event) => reject((event.target as IDBTransaction).error);
	});
}

/**
 * Get all keywords from IndexedDB
 */
export async function getKeywords(): Promise<KeywordWithDetail[]> {
	try {
		const keywords = await dbManager.getAll<KeywordWithDetail>(KEYWORDS_STORE_NAME);
		return keywords || [];
	} catch (error) {
		console.error('Failed to get keywords from IndexedDB:', error);
		return [];
	}
}

/**
 * Get a specific keyword by ID
 */
export async function getKeyword(id: string): Promise<KeywordWithDetail | null> {
	try {
		return await dbManager.get<KeywordWithDetail>(KEYWORDS_STORE_NAME, id);
	} catch (error) {
		console.error('Failed to get keyword from IndexedDB:', error);
		return null;
	}
}

/**
 * Delete a keyword from IndexedDB
 */
export async function deleteKeyword(id: string): Promise<void> {
	return dbManager.delete(KEYWORDS_STORE_NAME, id);
}

/**
 * Update a keyword in IndexedDB
 */
export async function updateKeyword(keyword: KeywordWithDetail): Promise<void> {
	return dbManager.put(KEYWORDS_STORE_NAME, keyword);
}

/**
 * Clear all keywords from IndexedDB
 */
export async function clearKeywords(): Promise<void> {
	return dbManager.clear(KEYWORDS_STORE_NAME);
}

/**
 * Save selected keywords to IndexedDB
 */
export async function saveSelectedKeywords(selectedIds: string[]): Promise<void> {
	const data: SelectedKeywordsData = {
		selectedIds,
		lastUpdated: Date.now(),
	};
	return dbManager.put(SELECTED_KEYWORDS_STORE_NAME, { id: 'selected', ...data });
}

/**
 * Get selected keywords from IndexedDB
 */
export async function getSelectedKeywords(): Promise<string[]> {
	try {
		const data = await dbManager.get<SelectedKeywordsData & { id: string }>(
			SELECTED_KEYWORDS_STORE_NAME,
			'selected'
		);
		return data?.selectedIds || [];
	} catch (error) {
		console.error('Failed to get selected keywords from IndexedDB:', error);
		return [];
	}
}

/**
 * Add a sync action to the queue
 */
export async function addToSyncQueue(action: KeywordSyncAction): Promise<void> {
	return dbManager.put(KEYWORD_SYNC_QUEUE_STORE_NAME, action);
}

/**
 * Get all sync actions from the queue
 */
export async function getSyncQueue(): Promise<KeywordSyncAction[]> {
	try {
		const actions = await dbManager.getAll<KeywordSyncAction>(KEYWORD_SYNC_QUEUE_STORE_NAME);
		return actions || [];
	} catch (error) {
		console.error('Failed to get sync queue from IndexedDB:', error);
		return [];
	}
}

/**
 * Remove a sync action from the queue
 */
export async function removeFromSyncQueue(actionId: string): Promise<void> {
	return dbManager.delete(KEYWORD_SYNC_QUEUE_STORE_NAME, actionId);
}

/**
 * Clear the entire sync queue
 */
export async function clearSyncQueue(): Promise<void> {
	return dbManager.clear(KEYWORD_SYNC_QUEUE_STORE_NAME);
}

/**
 * Clear all keyword-related data from IndexedDB
 */
export async function clearAllKeywordData(): Promise<void> {
	await Promise.all([
		clearKeywords(),
		dbManager.clear(SELECTED_KEYWORDS_STORE_NAME),
		clearSyncQueue(),
	]);
}

/**
 * Generate unique ID for sync actions
 */
export function generateActionId(): string {
	return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Search keywords by content
 */
export async function searchKeywords(term: string): Promise<KeywordWithDetail[]> {
	try {
		const allKeywords = await getKeywords();
		return allKeywords.filter((keyword) =>
			keyword.content.toLowerCase().includes(term.toLowerCase())
		);
	} catch (error) {
		console.error('Failed to search keywords:', error);
		return [];
	}
}
