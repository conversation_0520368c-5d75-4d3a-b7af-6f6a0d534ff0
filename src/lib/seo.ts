import { getSeoService } from '@/backend/wire';
import { SeoSettings } from '@prisma/client';
import { Metadata, Viewport } from 'next';

// Cache for SEO settings to avoid database calls on every request
let cachedSeoSettings: SeoSettings | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get SEO settings with caching
 */
export async function getSeoSettings(): Promise<SeoSettings> {
	const now = Date.now();

	// Return cached settings if still valid
	if (cachedSeoSettings && now - cacheTimestamp < CACHE_DURATION) {
		return cachedSeoSettings;
	}

	try {
		const seoService = getSeoService();
		const settings = await seoService.getOrCreateDefaultSeoSettings();

		// Update cache
		cachedSeoSettings = settings;
		cacheTimestamp = now;

		return settings;
	} catch (error) {
		console.error('Failed to get SEO settings:', error);

		// Return default settings if database fails
		return getDefaultSeoSettings();
	}
}

/**
 * Clear SEO settings cache (call after updates)
 */
export function clearSeoCache(): void {
	cachedSeoSettings = null;
	cacheTimestamp = 0;
}

/**
 * Get default SEO settings (fallback)
 */
function getDefaultSeoSettings(): SeoSettings {
	return {
		id: 'default',
		title: 'Vocab - Learn Vocabulary with AI',
		description:
			'Learn new vocabulary with AI assistance. Improve your English and Vietnamese vocabulary through spaced repetition and AI-generated content.',
		keywords: 'vocabulary, learning, AI, English, Vietnamese, education, spaced repetition',

		og_title: null,
		og_description: null,
		og_image_url: null,
		og_type: 'website',
		twitter_card: 'summary_large_image',
		twitter_site: null,
		twitter_creator: null,
		canonical_url: null,
		robots: 'index, follow',
		language: 'en',
		theme_color: '#000000',
		background_color: '#ffffff',
		created_at: new Date(),
		updated_at: new Date(),
	};
}

/**
 * Generate Next.js Metadata from SEO settings
 */
export async function generateMetadata(
	pageTitle?: string,
	pageDescription?: string,
	currentUrl?: string
): Promise<Metadata> {
	const settings = await getSeoSettings();

	const title = pageTitle || settings.title;
	const description = pageDescription || settings.description;
	const ogTitle = settings.og_title || title;
	const ogDescription = settings.og_description || description;

	const metadata: Metadata = {
		title,
		description,
		keywords: settings.keywords,
		robots: settings.robots,

		// Open Graph
		openGraph: {
			title: ogTitle,
			description: ogDescription,
			type: settings.og_type as any,
			url: currentUrl || settings.canonical_url || undefined,
			images: settings.og_image_url
				? [
						{
							url: settings.og_image_url,
							alt: ogTitle,
						},
				  ]
				: undefined,
		},

		// Twitter
		twitter: {
			card: settings.twitter_card as any,
			title: ogTitle,
			description: ogDescription,
			site: settings.twitter_site || undefined,
			creator: settings.twitter_creator || undefined,
			images: settings.og_image_url ? [settings.og_image_url] : undefined,
		},

		// Icons
		icons: {
			icon: '/favicon.ico',
			shortcut: '/favicon.ico',
			apple: '/favicon.ico',
		},

		// Manifest
		manifest: '/manifest.json',

		// Other metadata
		other: {
			'msapplication-TileColor': settings.background_color,
		},
	};

	// Add canonical URL if available
	if (currentUrl || settings.canonical_url) {
		metadata.alternates = {
			canonical: currentUrl || settings.canonical_url || undefined,
		};
	}

	return metadata;
}

/**
 * Generate Next.js Viewport from SEO settings
 */
export async function generateViewport(): Promise<Viewport> {
	const settings = await getSeoSettings();

	return {
		themeColor: settings.theme_color,
		width: 'device-width',
		initialScale: 1,
		maximumScale: 1,
		userScalable: false,
	};
}

/**
 * Generate HTML meta tags for server-side rendering
 */
export async function generateMetaTags(currentUrl?: string): Promise<string[]> {
	const settings = await getSeoSettings();
	const seoService = getSeoService();

	return seoService.generateMetaTags(settings, currentUrl);
}

/**
 * Update manifest.json with SEO settings
 */
export async function updateManifest(): Promise<any> {
	const settings = await getSeoSettings();

	return {
		name: settings.title,
		short_name: 'Vocab',
		description: settings.description,
		start_url: '/',
		display: 'standalone',
		background_color: settings.background_color,
		theme_color: settings.theme_color,
		orientation: 'portrait',
		scope: '/',
		dir: 'ltr',
		lang: settings.language,
		categories: ['education', 'productivity'],
		icons: [
			{
				src: '/icon-192x192.ico',
				sizes: '192x192',
				type: 'image/ico',
				purpose: 'any maskable',
			},
			{
				src: '/icon-512x512.ico',
				sizes: '512x512',
				type: 'image/ico',
				purpose: 'any maskable',
			},
		],
	};
}

/**
 * Validate SEO settings for common issues
 */
export function validateSeoSettings(settings: SeoSettings): {
	isValid: boolean;
	warnings: string[];
	errors: string[];
} {
	const warnings: string[] = [];
	const errors: string[] = [];

	// Title validation
	if (!settings.title) {
		errors.push('Title is required');
	} else if (settings.title.length > 60) {
		warnings.push('Title is longer than 60 characters, may be truncated in search results');
	}

	// Description validation
	if (!settings.description) {
		errors.push('Description is required');
	} else if (settings.description.length > 160) {
		warnings.push(
			'Description is longer than 160 characters, may be truncated in search results'
		);
	}

	// Image validation
	if (settings.og_image_url && !isValidUrl(settings.og_image_url)) {
		errors.push('Open Graph image URL is not valid');
	}

	// Missing Open Graph image warning
	if (!settings.og_image_url) {
		warnings.push(
			'No Open Graph image specified, social media previews may not display properly'
		);
	}

	return {
		isValid: errors.length === 0,
		warnings,
		errors,
	};
}

/**
 * Check if URL is valid
 */
function isValidUrl(url: string): boolean {
	try {
		new URL(url);
		return true;
	} catch {
		// Check if it's a relative URL
		return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
	}
}
