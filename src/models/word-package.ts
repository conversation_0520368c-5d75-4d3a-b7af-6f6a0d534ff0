import { Prisma, Language, Difficulty } from '@prisma/client';

export type WordPackage = Prisma.WordPackageGetPayload<{
	include: {
		words: true;
		user_selections: true;
	};
}>;

export type WordPackageWithStats = Prisma.WordPackageGetPayload<{
	include: {
		words: true;
		_count: {
			select: {
				user_selections: true;
			};
		};
	};
}>;

export type WordPackageWord = Prisma.WordPackageWordGetPayload<{}>;

export type UserWordPackage = Prisma.UserWordPackageGetPayload<{
	include: {
		word_package: {
			include: {
				words: true;
			};
		};
	};
}>;

export interface CreateWordPackageInput {
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	category: string;
	tags: string[];
	words: Array<{
		term: string;
		language: Language;
	}>;
}

export interface WordPackageFilter {
	source_language?: Language;
	target_language?: Language;
	difficulty?: Difficulty;
	category?: string;
	tags?: string[];
	search?: string;
	excludeUserPackages?: boolean;
}

export interface WordPackageStats {
	totalPackages: number;
	totalWords: number;
	categoriesCount: number;
	averageWordsPerPackage: number;
	popularCategories: Array<{
		category: string;
		count: number;
	}>;
}
