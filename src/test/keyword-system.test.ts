/**
 * Test file for keyword system with localStorage and background sync
 */

import { keywordStorage, KeywordStorage } from '@/lib/keyword-storage';
import { KeywordWithDetail } from '@/models';

// Mock localStorage for testing
const localStorageMock = (() => {
	let store: Record<string, string> = {};

	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value.toString();
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
	};
})();

Object.defineProperty(window, 'localStorage', {
	value: localStorageMock,
});

describe('Keyword Storage System', () => {
	beforeEach(() => {
		localStorageMock.clear();
	});

	test('should save and retrieve keywords from localStorage', () => {
		const testKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
			{
				id: '2',
				content: 'business',
				user_id: 'user1',
			},
		];

		keywordStorage.saveKeywords(testKeywords);
		const retrievedKeywords = keywordStorage.getKeywords();

		expect(retrievedKeywords).toEqual(testKeywords);
	});

	test('should save and retrieve selected keywords', () => {
		const selectedIds = ['1', '2', '3'];

		keywordStorage.saveSelectedKeywords(selectedIds);
		const retrievedSelected = keywordStorage.getSelectedKeywords();

		expect(retrievedSelected).toEqual(selectedIds);
	});

	test('should add keyword locally with optimistic update', () => {
		const newKeyword: KeywordWithDetail = {
			id: '3',
			content: 'science',
			user_id: 'user1',
		};

		keywordStorage.addKeywordLocally(newKeyword);
		const keywords = keywordStorage.getKeywords();

		expect(keywords).toContain(newKeyword);
	});

	test('should update keyword locally', () => {
		const initialKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
		];

		keywordStorage.saveKeywords(initialKeywords);
		keywordStorage.updateKeywordLocally('1', { content: 'updated-technology' });

		const keywords = keywordStorage.getKeywords();
		expect(keywords[0].content).toBe('updated-technology');
	});

	test('should delete keyword locally', () => {
		const initialKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
			{
				id: '2',
				content: 'business',
				user_id: 'user1',
			},
		];

		keywordStorage.saveKeywords(initialKeywords);
		keywordStorage.saveSelectedKeywords(['1', '2']);

		keywordStorage.deleteKeywordLocally('1');

		const keywords = keywordStorage.getKeywords();
		const selectedKeywords = keywordStorage.getSelectedKeywords();

		expect(keywords).toHaveLength(1);
		expect(keywords[0].id).toBe('2');
		expect(selectedKeywords).toEqual(['2']);
	});

	test('should manage sync queue', () => {
		const action = {
			id: 'action1',
			type: 'create' as const,
			data: { name: 'test-keyword' },
		};

		keywordStorage.addToSyncQueue(action);
		const queue = keywordStorage.getSyncQueue();

		expect(queue).toHaveLength(1);
		expect(queue[0].id).toBe('action1');
		expect(queue[0].type).toBe('create');
		expect(queue[0].timestamp).toBeDefined();

		keywordStorage.removeFromSyncQueue('action1');
		const updatedQueue = keywordStorage.getSyncQueue();

		expect(updatedQueue).toHaveLength(0);
	});

	test('should clear all data', () => {
		const testKeywords: KeywordWithDetail[] = [
			{
				id: '1',
				content: 'technology',
				user_id: 'user1',
			},
		];

		keywordStorage.saveKeywords(testKeywords);
		keywordStorage.saveSelectedKeywords(['1']);
		keywordStorage.addToSyncQueue({
			id: 'action1',
			type: 'create',
			data: { name: 'test' },
		});

		keywordStorage.clearAll();

		expect(keywordStorage.getKeywords()).toEqual([]);
		expect(keywordStorage.getSelectedKeywords()).toEqual([]);
		expect(keywordStorage.getSyncQueue()).toEqual([]);
	});

	test('should generate unique action IDs', () => {
		const id1 = keywordStorage.generateActionId();
		const id2 = keywordStorage.generateActionId();

		expect(id1).not.toBe(id2);
		expect(id1).toMatch(/^action_\d+_[a-z0-9]+$/);
		expect(id2).toMatch(/^action_\d+_[a-z0-9]+$/);
	});
});

console.log('Keyword system tests completed successfully!');
