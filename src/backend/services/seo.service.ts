import { SeoSettings } from '@prisma/client';
import { SeoRepository } from '../repositories/seo.repository';

export interface SeoService {
	getSeoSettings(): Promise<SeoSettings | null>;
	updateSeoSettings(data: Partial<SeoSettings>): Promise<SeoSettings>;
	getOrCreateDefaultSeoSettings(): Promise<SeoSettings>;
	resetToDefaults(): Promise<SeoSettings>;
	generateMetaTags(settings: SeoSettings, currentUrl?: string): string[];
}

export class SeoServiceImpl implements SeoService {
	constructor(private readonly getSeoRepository: () => SeoRepository) {}

	/**
	 * Get current SEO settings
	 */
	async getSeoSettings(): Promise<SeoSettings | null> {
		// Since we only have one SEO settings record, get the first one
		const settings = await this.getSeoRepository().findFirst();
		return settings;
	}

	/**
	 * Update SEO settings
	 */
	async updateSeoSettings(data: Partial<SeoSettings>): Promise<SeoSettings> {
		const repository = this.getSeoRepository();

		// Get existing settings or create default
		let existingSettings = await this.getSeoSettings();

		if (!existingSettings) {
			// Create new settings with provided data
			existingSettings = await repository.create({
				...this.getDefaultSeoData(),
				...data,
			});
		} else {
			// Update existing settings
			existingSettings = await repository.update(existingSettings.id, data);
		}

		return existingSettings;
	}

	/**
	 * Get or create default SEO settings
	 */
	async getOrCreateDefaultSeoSettings(): Promise<SeoSettings> {
		let settings = await this.getSeoSettings();

		if (!settings) {
			// Create default settings
			settings = await this.getSeoRepository().create(this.getDefaultSeoData());
		}

		return settings;
	}

	/**
	 * Reset SEO settings to defaults
	 */
	async resetToDefaults(): Promise<SeoSettings> {
		const repository = this.getSeoRepository();
		const existingSettings = await this.getSeoSettings();

		if (existingSettings) {
			// Update existing settings with defaults
			return await repository.update(existingSettings.id, this.getDefaultSeoData());
		} else {
			// Create new default settings
			return await repository.create(this.getDefaultSeoData());
		}
	}

	/**
	 * Get default SEO data
	 */
	private getDefaultSeoData(): Omit<SeoSettings, 'id' | 'created_at' | 'updated_at'> {
		return {
			title: 'Vocab - Learn Vocabulary with AI',
			description:
				'Learn new vocabulary with AI assistance. Improve your English and Vietnamese vocabulary through spaced repetition and AI-generated content.',
			keywords: 'vocabulary, learning, AI, English, Vietnamese, education, spaced repetition',

			og_title: null,
			og_description: null,
			og_image_url: null,
			og_type: 'website',
			twitter_card: 'summary_large_image',
			twitter_site: null,
			twitter_creator: null,
			canonical_url: null,
			robots: 'index, follow',
			language: 'en',
			theme_color: '#000000',
			background_color: '#ffffff',
		};
	}

	/**
	 * Validate SEO settings data
	 */
	validateSeoData(data: Partial<SeoSettings>): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];

		// Title validation
		if (data.title !== undefined) {
			if (!data.title || data.title.trim().length === 0) {
				errors.push('Title is required');
			} else if (data.title.length > 60) {
				errors.push('Title should be 60 characters or less for optimal SEO');
			}
		}

		// Description validation
		if (data.description !== undefined) {
			if (!data.description || data.description.trim().length === 0) {
				errors.push('Description is required');
			} else if (data.description.length > 160) {
				errors.push('Description should be 160 characters or less for optimal SEO');
			}
		}

		// Keywords validation
		if (data.keywords !== undefined) {
			if (data.keywords && data.keywords.length > 255) {
				errors.push('Keywords should be 255 characters or less');
			}
		}

		// URL validations
		const urlFields = ['og_image_url', 'canonical_url'] as const;
		urlFields.forEach((field) => {
			if (data[field] && !this.isValidUrl(data[field]!)) {
				errors.push(`${field.replace('_', ' ')} must be a valid URL`);
			}
		});

		// Twitter card validation
		if (data.twitter_card !== undefined) {
			const validCards = ['summary', 'summary_large_image', 'app', 'player'];
			if (data.twitter_card && !validCards.includes(data.twitter_card)) {
				errors.push(
					'Twitter card must be one of: summary, summary_large_image, app, player'
				);
			}
		}

		// Robots validation
		if (data.robots !== undefined) {
			const validRobots = ['index', 'noindex', 'follow', 'nofollow'];
			if (data.robots) {
				const robotsArray = data.robots.split(',').map((r) => r.trim());
				const invalidRobots = robotsArray.filter((r) => !validRobots.includes(r));
				if (invalidRobots.length > 0) {
					errors.push(`Invalid robots directive(s): ${invalidRobots.join(', ')}`);
				}
			}
		}

		// Language validation
		if (data.language !== undefined) {
			if (data.language && !/^[a-z]{2}(-[A-Z]{2})?$/.test(data.language)) {
				errors.push('Language must be in ISO 639-1 format (e.g., "en", "en-US")');
			}
		}

		// Color validation
		const colorFields = ['theme_color', 'background_color'] as const;
		colorFields.forEach((field) => {
			if (data[field] && !this.isValidColor(data[field]!)) {
				errors.push(`${field.replace('_', ' ')} must be a valid hex color (e.g., #000000)`);
			}
		});

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Check if URL is valid
	 */
	private isValidUrl(url: string): boolean {
		try {
			new URL(url);
			return true;
		} catch {
			// Check if it's a relative URL
			return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
		}
	}

	/**
	 * Check if color is valid hex color
	 */
	private isValidColor(color: string): boolean {
		return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
	}

	/**
	 * Generate meta tags for HTML head
	 */
	generateMetaTags(settings: SeoSettings, currentUrl?: string): string[] {
		const tags: string[] = [];

		// Basic meta tags
		tags.push(`<title>${this.escapeHtml(settings.title)}</title>`);
		tags.push(`<meta name="description" content="${this.escapeHtml(settings.description)}" />`);
		tags.push(`<meta name="keywords" content="${this.escapeHtml(settings.keywords)}" />`);
		tags.push(`<meta name="robots" content="${settings.robots}" />`);
		tags.push(`<meta name="language" content="${settings.language}" />`);

		// Theme colors
		tags.push(`<meta name="theme-color" content="${settings.theme_color}" />`);
		tags.push(`<meta name="msapplication-TileColor" content="${settings.background_color}" />`);

		// Default favicon
		tags.push(`<link rel="icon" href="/favicon.ico" />`);

		// Canonical URL
		if (settings.canonical_url || currentUrl) {
			const canonicalUrl = settings.canonical_url || currentUrl;
			tags.push(`<link rel="canonical" href="${canonicalUrl}" />`);
		}

		// Open Graph tags
		const ogTitle = settings.og_title || settings.title;
		const ogDescription = settings.og_description || settings.description;

		tags.push(`<meta property="og:title" content="${this.escapeHtml(ogTitle)}" />`);
		tags.push(`<meta property="og:description" content="${this.escapeHtml(ogDescription)}" />`);
		tags.push(`<meta property="og:type" content="${settings.og_type}" />`);

		if (settings.og_image_url) {
			tags.push(`<meta property="og:image" content="${settings.og_image_url}" />`);
		}

		if (currentUrl) {
			tags.push(`<meta property="og:url" content="${currentUrl}" />`);
		}

		// Twitter Card tags
		tags.push(`<meta name="twitter:card" content="${settings.twitter_card}" />`);
		tags.push(`<meta name="twitter:title" content="${this.escapeHtml(ogTitle)}" />`);
		tags.push(
			`<meta name="twitter:description" content="${this.escapeHtml(ogDescription)}" />`
		);

		if (settings.twitter_site) {
			tags.push(`<meta name="twitter:site" content="${settings.twitter_site}" />`);
		}

		if (settings.twitter_creator) {
			tags.push(`<meta name="twitter:creator" content="${settings.twitter_creator}" />`);
		}

		if (settings.og_image_url) {
			tags.push(`<meta name="twitter:image" content="${settings.og_image_url}" />`);
		}

		return tags;
	}

	/**
	 * Escape HTML characters
	 */
	private escapeHtml(text: string): string {
		const map: { [key: string]: string } = {
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;',
			'"': '&quot;',
			"'": '&#039;',
		};
		return text.replace(/[&<>"']/g, (m) => map[m]);
	}
}
