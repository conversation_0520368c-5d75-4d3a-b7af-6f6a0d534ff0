import * as natural from 'natural';
import { Language, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface WordNetInfo {
	synsets: string[];
	lemma: string | null;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
}

export interface WordNetEntry {
	id: string;
	term: string;
	pos: string;
	synsets: string[];
	lemma: string | null;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
	created_at: Date;
	updated_at: Date;
}

export interface WordNetService {
	getWordNetInfo(term: string, language: Language): Promise<WordNetInfo>;
	getWordNetInfoFromDatabase(term: string, language: Language): Promise<WordNetInfo | null>;
	searchWordNetEntries(term: string, limit?: number): Promise<WordNetEntry[]>;
	getAllWordNetEntries(
		page?: number,
		limit?: number
	): Promise<{
		entries: WordNetEntry[];
		pagination: {
			page: number;
			limit: number;
			totalCount: number;
			totalPages: number;
			hasNextPage: boolean;
		};
	}>;
	isWordNetAvailable(): boolean;
}

export class WordNetServiceImpl implements WordNetService {
	private wordNet: any;
	private isInitialized = false;

	constructor() {
		try {
			this.wordNet = new natural.WordNet();
		} catch (error) {
			console.error('Failed to initialize WordNet:', error);
			this.wordNet = null;
		}
	}

	/**
	 * Initialize WordNet if not already initialized
	 */
	private async ensureInitialized(): Promise<void> {
		if (this.isInitialized) return;

		try {
			// WordNet is initialized in constructor
			this.isInitialized = true;
		} catch (error) {
			console.error('Failed to initialize WordNet:', error);
			throw new Error('WordNet initialization failed');
		}
	}

	/**
	 * Check if WordNet is available
	 */
	isWordNetAvailable(): boolean {
		return !!this.wordNet;
	}

	/**
	 * Get WordNet information for a term (database-first approach)
	 */
	async getWordNetInfo(term: string, language: Language): Promise<WordNetInfo> {
		// WordNet is primarily for English, so we only process English terms
		if (language !== Language.EN) {
			return this.getEmptyWordNetInfo();
		}

		// Try to get from database first
		const dbResult = await this.getWordNetInfoFromDatabase(term, language);
		if (dbResult) {
			return dbResult;
		}

		// Fallback to real-time lookup
		await this.ensureInitialized();

		if (!this.isWordNetAvailable()) {
			console.warn('WordNet is not available');
			return this.getEmptyWordNetInfo();
		}

		try {
			const wordNetInfo = await this.extractWordNetData(term);
			return wordNetInfo;
		} catch (error) {
			console.error(`Error getting WordNet info for term "${term}":`, error);
			return this.getEmptyWordNetInfo();
		}
	}

	/**
	 * Get WordNet information from database
	 */
	async getWordNetInfoFromDatabase(
		term: string,
		language: Language
	): Promise<WordNetInfo | null> {
		if (language !== Language.EN) {
			return null;
		}

		try {
			// Query WordNet data directly by term
			const wordNetEntries = await prisma.wordNetData.findMany({
				where: {
					term: term.toLowerCase(),
				},
			});

			if (wordNetEntries.length === 0) {
				return null;
			}

			// Combine data from all parts of speech for this term
			const combinedData: WordNetInfo = {
				synsets: [],
				lemma: null,
				hypernyms: [],
				hyponyms: [],
				holonyms: [],
				meronyms: [],
			};

			for (const entry of wordNetEntries) {
				combinedData.synsets.push(...(entry.synsets || []));
				if (entry.lemma && !combinedData.lemma) {
					combinedData.lemma = entry.lemma;
				}
				combinedData.hypernyms.push(...(entry.hypernyms || []));
				combinedData.hyponyms.push(...(entry.hyponyms || []));
				combinedData.holonyms.push(...(entry.holonyms || []));
				combinedData.meronyms.push(...(entry.meronyms || []));
			}

			// Remove duplicates
			combinedData.synsets = [...new Set(combinedData.synsets)];
			combinedData.hypernyms = [...new Set(combinedData.hypernyms)];
			combinedData.hyponyms = [...new Set(combinedData.hyponyms)];
			combinedData.holonyms = [...new Set(combinedData.holonyms)];
			combinedData.meronyms = [...new Set(combinedData.meronyms)];

			return combinedData;
		} catch (error) {
			console.error(`Error getting WordNet info from database for term "${term}":`, error);
			return null;
		}
	}

	/**
	 * Extract WordNet data for a term
	 */
	private async extractWordNetData(term: string): Promise<WordNetInfo> {
		return new Promise((resolve) => {
			const result: WordNetInfo = {
				synsets: [],
				lemma: null,
				hypernyms: [],
				hyponyms: [],
				holonyms: [],
				meronyms: [],
			};

			try {
				// Get synsets for the term
				this.wordNet.lookup(term, (results: any[]) => {
					if (!results || results.length === 0) {
						resolve(result);
						return;
					}

					// Set lemma (base form)
					result.lemma = term.toLowerCase();

					// Process each synset
					results.forEach((synset: any) => {
						// Add synset definition (gloss)
						if (synset.gloss) {
							result.synsets.push(synset.gloss);
						}

						// Add synonyms as related terms
						if (synset.synonyms && Array.isArray(synset.synonyms)) {
							synset.synonyms.forEach((synonym: string) => {
								if (synonym !== term && !result.hypernyms.includes(synonym)) {
									result.hypernyms.push(synonym);
								}
							});
						}

						// For now, we'll use synonyms as hypernyms since natural's WordNet
						// doesn't directly expose hypernyms/hyponyms in the basic API
						// In a more advanced implementation, we could use the synset offset
						// to look up related synsets
					});

					resolve(result);
				});
			} catch (error) {
				console.error('Error in WordNet lookup:', error);
				resolve(result);
			}
		});
	}

	/**
	 * Search WordNet entries by term
	 */
	async searchWordNetEntries(term: string, limit = 20): Promise<WordNetEntry[]> {
		try {
			const entries = await prisma.wordNetData.findMany({
				where: {
					term: {
						contains: term.toLowerCase(),
						mode: 'insensitive',
					},
				},
				orderBy: [
					{
						term: 'asc',
					},
					{
						pos: 'asc',
					},
				],
				take: limit,
			});

			return entries;
		} catch (error) {
			console.error(`Error searching WordNet entries for term "${term}":`, error);
			return [];
		}
	}

	/**
	 * Get all WordNet entries with pagination
	 */
	async getAllWordNetEntries(
		page = 1,
		limit = 20
	): Promise<{
		entries: WordNetEntry[];
		pagination: {
			page: number;
			limit: number;
			totalCount: number;
			totalPages: number;
			hasNextPage: boolean;
		};
	}> {
		try {
			const offset = (page - 1) * limit;

			// Get total count
			const totalCount = await prisma.wordNetData.count();

			// Get entries with pagination
			const entries = await prisma.wordNetData.findMany({
				orderBy: [
					{
						term: 'asc',
					},
					{
						pos: 'asc',
					},
				],
				skip: offset,
				take: limit,
			});

			const totalPages = Math.ceil(totalCount / limit);
			const hasNextPage = page < totalPages;

			return {
				entries,
				pagination: {
					page,
					limit,
					totalCount,
					totalPages,
					hasNextPage,
				},
			};
		} catch (error) {
			console.error('Error getting all WordNet entries:', error);
			return {
				entries: [],
				pagination: {
					page: 1,
					limit,
					totalCount: 0,
					totalPages: 0,
					hasNextPage: false,
				},
			};
		}
	}

	/**
	 * Get empty WordNet info structure
	 */
	private getEmptyWordNetInfo(): WordNetInfo {
		return {
			synsets: [],
			lemma: null,
			hypernyms: [],
			hyponyms: [],
			holonyms: [],
			meronyms: [],
		};
	}
}
