import type { Language, Difficulty, Prisma, PrismaClient } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';
import { WordPackageFilter } from '@/models/word-package';

// Define the include structure for WordPackage queries
export const wordPackageInclude = {
	words: true,
	user_selections: true,
} as const;

export const wordPackageWithStatsInclude = {
	words: true,
	_count: {
		select: {
			user_selections: true,
		},
	},
} as const;

export interface WordPackageRepository extends BaseRepository<any> {
	findAvailablePackages(
		userId: string,
		filter?: WordPackageFilter,
		limit?: number
	): Promise<any[]>;
	findUserPackages(
		userId: string,
		limit?: number,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]>;
	findPackageById(id: string): Promise<any | null>;
	createPackage(data: Prisma.WordPackageCreateInput): Promise<any>;
	addWordsToPackage(
		packageId: string,
		words: Array<{ term: string; language: Language }>
	): Promise<void>;
	selectPackageForUser(userId: string, packageId: string): Promise<any>;
	hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean>;
	getPackageStats(): Promise<any>;
	findPackagesByCategory(category: string, limit?: number): Promise<any[]>;
	searchPackages(query: string, limit?: number): Promise<any[]>;
}

export class WordPackageRepositoryImpl
	extends BaseRepositoryImpl<any>
	implements WordPackageRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.wordPackage);
	}

	async findAvailablePackages(
		userId: string,
		filter: WordPackageFilter = {},
		limit = 20
	): Promise<any[]> {
		const where: Prisma.WordPackageWhereInput = {
			is_active: true,
		};

		// Filter by source language
		if (filter.source_language) {
			where.source_language = filter.source_language;
		}

		// Filter by target language
		if (filter.target_language) {
			where.target_language = filter.target_language;
		}

		// Filter by difficulty
		if (filter.difficulty) {
			where.difficulty = filter.difficulty;
		}

		// Filter by category
		if (filter.category) {
			where.category = {
				contains: filter.category,
				mode: 'insensitive',
			};
		}

		// Filter by tags
		if (filter.tags && filter.tags.length > 0) {
			where.tags = {
				hasSome: filter.tags,
			};
		}

		// Search in name and description
		if (filter.search) {
			where.OR = [
				{
					name: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
				{
					description: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
			];
		}

		// Exclude packages already selected by user
		if (filter.excludeUserPackages) {
			where.user_selections = {
				none: {
					user_id: userId,
				},
			};
		}

		const packages = await this.prisma.wordPackage.findMany({
			where,
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}

	async findUserPackages(
		userId: string,
		limit = 50,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]> {
		const where: any = {
			user_id: userId,
		};

		// Filter by both source and target languages if provided
		if (source_language || target_language) {
			where.word_package = {};

			if (source_language) {
				where.word_package.source_language = source_language;
			}

			if (target_language) {
				where.word_package.target_language = target_language;
			}
		}

		const userPackages = await this.prisma.userWordPackage.findMany({
			where,
			include: {
				word_package: {
					include: wordPackageInclude,
				},
			},
			orderBy: {
				selected_at: 'desc',
			},
			take: limit,
		});

		return userPackages;
	}

	async findPackageById(id: string): Promise<any | null> {
		const wordPackage = await this.prisma.wordPackage.findUnique({
			where: { id },
			include: wordPackageInclude,
		});

		return wordPackage;
	}

	async createPackage(data: Prisma.WordPackageCreateInput): Promise<any> {
		const wordPackage = await this.prisma.wordPackage.create({
			data,
			include: wordPackageInclude,
		});

		return wordPackage;
	}

	async addWordsToPackage(
		packageId: string,
		words: Array<{ term: string; language: Language }>
	): Promise<void> {
		const wordData = words.map((word) => ({
			word_package_id: packageId,
			term: word.term.toLowerCase(),
			language: word.language,
		}));

		await this.prisma.wordPackageWord.createMany({
			data: wordData,
			skipDuplicates: true,
		});

		// Update word count
		const wordCount = await this.prisma.wordPackageWord.count({
			where: { word_package_id: packageId },
		});

		await this.prisma.wordPackage.update({
			where: { id: packageId },
			data: { word_count: wordCount },
		});
	}

	async selectPackageForUser(userId: string, packageId: string): Promise<any> {
		const selection = await this.prisma.userWordPackage.create({
			data: {
				user_id: userId,
				word_package_id: packageId,
			},
			include: {
				word_package: {
					include: wordPackageInclude,
				},
			},
		});

		return selection;
	}

	async hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean> {
		const selection = await this.prisma.userWordPackage.findUnique({
			where: {
				user_id_word_package_id: {
					user_id: userId,
					word_package_id: packageId,
				},
			},
		});

		return !!selection;
	}

	async getPackageStats(): Promise<any> {
		const [totalPackages, totalWords, categories] = await Promise.all([
			this.prisma.wordPackage.count({
				where: { is_active: true },
			}),
			this.prisma.wordPackageWord.count(),
			this.prisma.wordPackage.groupBy({
				by: ['category'],
				where: { is_active: true },
				_count: {
					category: true,
				},
				orderBy: {
					_count: {
						category: 'desc',
					},
				},
				take: 10,
			}),
		]);

		const averageWordsPerPackage =
			totalPackages > 0 ? Math.round(totalWords / totalPackages) : 0;

		return {
			totalPackages,
			totalWords,
			categoriesCount: categories.length,
			averageWordsPerPackage,
			popularCategories: categories.map((cat) => ({
				category: cat.category,
				count: cat._count.category,
			})),
		};
	}

	async findPackagesByCategory(category: string, limit = 20): Promise<any[]> {
		const packages = await this.prisma.wordPackage.findMany({
			where: {
				is_active: true,
				category: {
					contains: category,
					mode: 'insensitive',
				},
			},
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}

	async searchPackages(query: string, limit = 20): Promise<any[]> {
		const packages = await this.prisma.wordPackage.findMany({
			where: {
				is_active: true,
				OR: [
					{
						name: {
							contains: query,
							mode: 'insensitive',
						},
					},
					{
						description: {
							contains: query,
							mode: 'insensitive',
						},
					},
					{
						category: {
							contains: query,
							mode: 'insensitive',
						},
					},
					{
						tags: {
							hasSome: [query],
						},
					},
				],
			},
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}
}
