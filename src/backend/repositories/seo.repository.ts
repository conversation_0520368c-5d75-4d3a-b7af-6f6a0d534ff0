import { SeoSettings } from '@prisma/client';
import { BaseRepository, BaseRepositoryImpl } from './base.repository';
import { getPrismaClient } from '../wire';

export interface SeoRepository extends BaseRepository<SeoSettings> {
	findFirst(): Promise<SeoSettings | null>;
}

export class SeoRepositoryImpl extends BaseRepositoryImpl<SeoSettings> implements SeoRepository {
	constructor() {
		const prisma = getPrismaClient();
		super(prisma.seoSettings);
	}

	/**
	 * Find the first (and should be only) SEO settings record
	 */
	async findFirst(): Promise<SeoSettings | null> {
		try {
			const prisma = getPrismaClient();
			const settings = await prisma.seoSettings.findFirst({
				orderBy: {
					created_at: 'asc', // Get the oldest record (first created)
				},
			});
			return settings;
		} catch (error) {
			console.error('Failed to find first SEO settings:', error);
			throw error;
		}
	}

	/**
	 * Override create to ensure only one SEO settings record exists
	 */
	async create(
		data: Omit<SeoSettings, 'id' | 'created_at' | 'updated_at'>
	): Promise<SeoSettings> {
		try {
			// Check if settings already exist
			const existingSettings = await this.findFirst();
			if (existingSettings) {
				// Update existing instead of creating new
				return this.update(existingSettings.id, data);
			}

			// Create new settings
			const prisma = getPrismaClient();
			const settings = await prisma.seoSettings.create({
				data,
			});
			return settings;
		} catch (error) {
			console.error('Failed to create SEO settings:', error);
			throw error;
		}
	}

	/**
	 * Override update with specific SEO settings logic
	 */
	async update(id: string, data: Partial<SeoSettings>): Promise<SeoSettings> {
		try {
			const prisma = getPrismaClient();
			const settings = await prisma.seoSettings.update({
				where: { id },
				data: {
					...data,
					updated_at: new Date(),
				},
			});
			return settings;
		} catch (error) {
			console.error('Failed to update SEO settings:', error);
			throw error;
		}
	}

	/**
	 * Get all SEO settings (should only be one)
	 */
	async find(): Promise<SeoSettings[]> {
		try {
			const prisma = getPrismaClient();
			const settings = await prisma.seoSettings.findMany({
				orderBy: {
					created_at: 'asc',
				},
			});
			return settings;
		} catch (error) {
			console.error('Failed to find SEO settings:', error);
			throw error;
		}
	}

	/**
	 * Delete SEO settings (not recommended, but available)
	 */
	async delete(query: Record<string, unknown>): Promise<void> {
		try {
			const prisma = getPrismaClient();
			await prisma.seoSettings.deleteMany({
				where: query,
			});
		} catch (error) {
			console.error('Failed to delete SEO settings:', error);
			throw error;
		}
	}

	/**
	 * Check if SEO settings exist
	 */
	async exists(): Promise<boolean> {
		try {
			const prisma = getPrismaClient();
			const count = await prisma.seoSettings.count();
			return count > 0;
		} catch (error) {
			console.error('Failed to check if SEO settings exist:', error);
			return false;
		}
	}

	/**
	 * Reset SEO settings to default values
	 */
	async resetToDefaults(): Promise<SeoSettings> {
		try {
			const prisma = getPrismaClient();
			// Delete all existing settings
			await prisma.seoSettings.deleteMany();

			// Create new default settings
			const defaultSettings = await prisma.seoSettings.create({
				data: {
					title: 'Vocab - Learn Vocabulary with AI',
					description:
						'Learn new vocabulary with AI assistance. Improve your English and Vietnamese vocabulary through spaced repetition and AI-generated content.',
					keywords:
						'vocabulary, learning, AI, English, Vietnamese, education, spaced repetition',

					og_title: null,
					og_description: null,
					og_image_url: null,
					og_type: 'website',
					twitter_card: 'summary_large_image',
					twitter_site: null,
					twitter_creator: null,
					canonical_url: null,
					robots: 'index, follow',
					language: 'en',
					theme_color: '#000000',
					background_color: '#ffffff',
				},
			});

			return defaultSettings;
		} catch (error) {
			console.error('Failed to reset SEO settings to defaults:', error);
			throw error;
		}
	}
}
