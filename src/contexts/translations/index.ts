import { Language } from '@prisma/client';
import { aboutTranslations } from './about.trans';
import { accessibilityTranslations } from './accessibility.trans';
import { collectionsTranslations } from './collections.trans';
import { commonTranslations } from './common.trans';
import { difficultyTranslations } from './difficulty.trans';
import { errorsTranslations } from './errors.trans';
import { feedbackTranslations } from './feedback.trans';
import { grammarTranslations } from './grammar.trans';
import { headingsTranslations } from './headings.trans';
import { homeTranslations } from './home.trans';
import { languageTranslations } from './language.trans';
import { legalTranslations } from './legal.trans';
import { lengthsTranslations } from './lengths.trans';
import { navTranslations } from './nav.trans';
import { paragraphsTranslations } from './paragraphs.trans';
import { qaPracticeTranslations } from './qa-practice.trans';
import { reviewTranslations } from './review.trans';
import { searchTranslations } from './search.trans';
import { settingsTranslations } from './settings.trans';
import { themeTranslations } from './theme.trans';
import { toastTranslations } from './toast.trans';
import { uiTranslations } from './ui.trans';
import { wordsTranslations } from './words.trans';
import { keywordsTranslations } from './keywords.trans';
import { wordnetTranslations } from './wordnet.trans';
import { vocabularyLookupTranslations } from './vocabulary-lookup.trans';
import { wordnetLookupTranslations } from './wordnet-lookup.trans';

export * from './about.trans';
export * from './accessibility.trans';
export * from './collections.trans';
export * from './common.trans';
export * from './difficulty.trans';
export * from './errors.trans';
export * from './feedback.trans';
export * from './grammar.trans';
export * from './headings.trans';
export * from './home.trans';
export * from './keywords.trans';
export * from './language.trans';
export * from './legal.trans';
export * from './lengths.trans';
export * from './nav.trans';
export * from './paragraphs.trans';
export * from './qa-practice.trans';
export * from './review.trans';
export * from './search.trans';
export * from './settings.trans';
export * from './theme.trans';
export * from './ui.trans';
export * from './words.trans';
export * from './wordnet.trans';
export * from './vocabulary-lookup.trans';
export * from './wordnet-lookup.trans';
export * from './toast.trans';

// Define the structure for translation entries
interface TranslationDict {
	[key: string]: Record<Language, string> | Record<Language, any>;
}

// Combine all the smaller translation objects into one
export const translations = {
	...navTranslations,
	...homeTranslations,
	...feedbackTranslations,
	...aboutTranslations,
	...uiTranslations,
	...collectionsTranslations,
	...themeTranslations,
	...searchTranslations,
	...wordsTranslations,
	...accessibilityTranslations,
	...commonTranslations,
	...reviewTranslations,
	...legalTranslations,
	...languageTranslations,
	...headingsTranslations,
	...lengthsTranslations,
	...paragraphsTranslations,
	...qaPracticeTranslations,
	...settingsTranslations,
	...toastTranslations,
	...grammarTranslations,
	...difficultyTranslations,
	...keywordsTranslations,
	...wordnetTranslations,
	...vocabularyLookupTranslations,
	...wordnetLookupTranslations,
	...errorsTranslations,
} as unknown as TranslationDict;

export function getTranslationKeyOfLanguage(lang: Language) {
	switch (lang) {
		case 'EN':
			return 'language.EN';
		case 'VI':
			return 'language.VI';
	}
}
