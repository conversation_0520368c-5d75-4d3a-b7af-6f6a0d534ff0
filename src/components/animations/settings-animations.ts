import { Variants } from 'framer-motion';

// Settings panel animations
export const settingsPanelVariants: Variants = {
	hidden: {
		opacity: 0,
		scale: 0.95,
		y: 10,
	},
	visible: {
		opacity: 1,
		scale: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
			staggerChildren: 0.05,
		},
	},
	exit: {
		opacity: 0,
		scale: 0.95,
		y: 10,
		transition: {
			duration: 0.15,
			ease: 'easeIn',
		},
	},
};

// Settings section animations
export const settingsSectionVariants: Variants = {
	hidden: {
		opacity: 0,
		x: -10,
	},
	visible: {
		opacity: 1,
		x: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

// Settings button animations
export const settingsButtonVariants: Variants = {
	idle: {
		scale: 1,
		rotate: 0,
	},
	hover: {
		scale: 1.05,
		rotate: 90,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
	tap: {
		scale: 0.95,
		transition: {
			duration: 0.1,
		},
	},
};

// Settings item animations
export const settingsItemVariants: Variants = {
	hidden: {
		opacity: 0,
		y: 5,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.15,
			ease: 'easeOut',
		},
	},
	hover: {
		x: 2,
		transition: {
			duration: 0.1,
		},
	},
};

// Backdrop animations
export const backdropVariants: Variants = {
	hidden: {
		opacity: 0,
	},
	visible: {
		opacity: 1,
		transition: {
			duration: 0.2,
		},
	},
	exit: {
		opacity: 0,
		transition: {
			duration: 0.15,
		},
	},
};

// Guidance button animations
export const guidanceButtonVariants: Variants = {
	idle: {
		scale: 1,
		rotate: 0,
		boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3)',
	},
	hover: {
		scale: 1.05,
		rotate: 5,
		boxShadow: '0 15px 35px rgba(59, 130, 246, 0.4)',
		transition: {
			type: 'spring',
			stiffness: 400,
			damping: 10,
		},
	},
	tap: {
		scale: 0.95,
		rotate: -2,
		transition: {
			type: 'spring',
			stiffness: 600,
			damping: 15,
		},
	},
};

// Back to top button animations (no rotation effects)
export const backToTopButtonVariants: Variants = {
	idle: {
		scale: 1,
	},
	hover: {
		scale: 1.05,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
	tap: {
		scale: 0.95,
		transition: {
			duration: 0.1,
		},
	},
};
