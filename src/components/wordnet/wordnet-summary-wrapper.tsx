'use client';

import { Language } from '@prisma/client';
import { useWordNet } from '@/hooks/use-wordnet';
import { WordNetSummary } from './wordnet-summary';

interface WordNetSummaryWrapperProps {
	term: string;
	language?: Language;
	enabled?: boolean;
}

export function WordNetSummaryWrapper({ 
	term, 
	language = Language.EN, 
	enabled = true 
}: WordNetSummaryWrapperProps) {
	const { wordNetData, isLoading } = useWordNet({ term, language, enabled });

	if (!enabled || language !== Language.EN || isLoading) {
		return null;
	}

	if (!wordNetData) {
		return null;
	}

	return <WordNetSummary wordNetData={wordNetData} />;
}
