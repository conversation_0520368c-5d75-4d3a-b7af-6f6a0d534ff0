'use client';

import { Language } from '@prisma/client';
import { useWordNet } from '@/hooks/use-wordnet';
import { WordNetInfo } from './wordnet-info';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Network, Loader2 } from 'lucide-react';
import { useTranslation } from '@/contexts/translation-context';

interface WordNetInfoWrapperProps {
	term: string;
	language?: Language;
	className?: string;
	enabled?: boolean;
}

export function WordNetInfoWrapper({ 
	term, 
	language = Language.EN, 
	className,
	enabled = true 
}: WordNetInfoWrapperProps) {
	const { t } = useTranslation();
	const { wordNetData, isLoading, error } = useWordNet({ term, language, enabled });

	if (!enabled || language !== Language.EN) {
		return null;
	}

	if (isLoading) {
		return (
			<Card className={className}>
				<CardHeader className="pb-3">
					<CardTitle className="text-sm flex items-center gap-2">
						<Network className="w-4 h-4" />
						{t('wordnet.title')}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center gap-2 text-sm text-muted-foreground">
						<Loader2 className="w-4 h-4 animate-spin" />
						{t('wordnet.loading')}
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className={className}>
				<CardHeader className="pb-3">
					<CardTitle className="text-sm flex items-center gap-2">
						<Network className="w-4 h-4" />
						{t('wordnet.title')}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground italic">
						{t('wordnet.error')}
					</p>
				</CardContent>
			</Card>
		);
	}

	if (!wordNetData) {
		return (
			<Card className={className}>
				<CardHeader className="pb-3">
					<CardTitle className="text-sm flex items-center gap-2">
						<Network className="w-4 h-4" />
						{t('wordnet.title')}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground italic">
						{t('wordnet.noData')}
					</p>
				</CardContent>
			</Card>
		);
	}

	return <WordNetInfo wordNetData={wordNetData} term={term} className={className} />;
}
