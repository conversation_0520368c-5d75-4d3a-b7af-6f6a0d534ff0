'use client';

import { <PERSON><PERSON>, Card, Separator, useTheme } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useAuth } from '@/contexts/auth-context';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Language } from '@prisma/client';
import { motion, AnimatePresence } from 'framer-motion';
import {
	Languages,
	LogOut,
	Monitor,
	Moon,
	Settings,
	Sun,
	User,
	X,
	Key,
	Shield,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import {
	settingsPanelVariants,
	settingsSectionVariants,
	settingsItemVariants,
	backdropVariants,
} from '@/components/animations/settings-animations';
import { ChangePasswordForm } from '@/components/auth/change-password-form';

interface EnhancedSettingsPanelProps {
	isOpen: boolean;
	onClose: () => void;
}

export function EnhancedSettingsPanel({ isOpen, onClose }: EnhancedSettingsPanelProps) {
	const { theme, setTheme } = useTheme();
	const { language, setLanguage, t } = useTranslation();
	const { logout, user } = useAuth();
	const router = useRouter();

	// State for change password dialog
	const [showChangePassword, setShowChangePassword] = useState(false);

	const handleLogout = async () => {
		try {
			await logout();
			onClose(); // Close the settings dialog
			router.push('/login');
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
		setTheme(newTheme);
	};

	const handleLanguageChange = (newLanguage: Language) => {
		setLanguage(newLanguage);
	};

	const handleChangePassword = () => {
		setShowChangePassword(true);
	};

	const handleChangePasswordSuccess = () => {
		setShowChangePassword(false);
	};

	const handleChangePasswordCancel = () => {
		setShowChangePassword(false);
	};

	if (!isOpen) {
		return null;
	}

	return (
		<>
			<AnimatePresence>
				<motion.div
					className="fixed inset-0 z-[1300] flex items-center justify-center p-4"
					variants={backdropVariants}
					initial="hidden"
					animate="visible"
					exit="exit"
				>
					<motion.div
						className="absolute inset-0 bg-black/20 backdrop-blur-sm"
						onClick={onClose}
					/>
					<motion.div
						variants={settingsPanelVariants}
						initial="hidden"
						animate="visible"
						exit="exit"
						className="relative z-10"
					>
						<Card className="w-full max-w-4xl lg:max-w-5xl p-6 bg-background/95 backdrop-blur-sm border shadow-xl">
							{/* Header */}
							<motion.div
								variants={settingsSectionVariants}
								className="flex items-center justify-between mb-6"
							>
								<h3 className="text-xl font-semibold flex items-center gap-2">
									<Settings className="h-5 w-5 text-primary" />
									{t('settings.title')}
								</h3>
								<motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
									<Button
										size="icon"
										variant="ghost"
										className="h-8 w-8"
										onClick={onClose}
									>
										<X className="h-4 w-4" />
									</Button>
								</motion.div>
							</motion.div>

							{/* Main Content Grid */}
							<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
								{/* User Account Section */}
								{user && (
									<motion.div
										variants={settingsSectionVariants}
										className="lg:col-span-1"
									>
										<Card className="p-4 bg-accent/20 border-accent/30">
											<div className="flex items-center gap-2 mb-4">
												<User className="h-5 w-5 text-primary" />
												<span className="text-lg font-semibold">
													{t('user.account')}
												</span>
											</div>

											{/* User Info */}
											<div className="space-y-3 mb-4">
												<div className="flex flex-col space-y-1">
													<span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
														{t('user.username')}
													</span>
													<span className="text-sm font-medium">
														{user.username || user.provider_id}
													</span>
												</div>

												<div className="flex flex-col space-y-1">
													<span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
														{t('user.provider')}
													</span>
													<span className="text-sm">
														{user.provider === 'USERNAME_PASSWORD'
															? 'Username/Password'
															: user.provider === 'TELEGRAM'
															? 'Telegram'
															: user.provider === 'GOOGLE'
															? 'Google'
															: user.provider}
													</span>
												</div>

												<div className="flex flex-col space-y-1">
													<span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
														{t('user.role')}
													</span>
													<span className="text-sm">
														{user.role === 'ADMIN'
															? 'Administrator'
															: 'User'}
													</span>
												</div>
											</div>

											{/* Security Section */}
											{user.provider === 'USERNAME_PASSWORD' && (
												<>
													<Separator className="my-4" />
													<div className="flex items-center gap-2 mb-3">
														<Shield className="h-4 w-4 text-muted-foreground" />
														<span className="text-sm font-medium">
															{t('settings.security')}
														</span>
													</div>
													<motion.div
														variants={settingsItemVariants}
														whileHover="hover"
													>
														<Button
															variant="outline"
															size="sm"
															className="w-full"
															onClick={handleChangePassword}
														>
															<Key className="h-4 w-4 mr-2" />
															{t('settings.change_password')}
														</Button>
													</motion.div>
												</>
											)}

											<Separator className="my-4" />

											{/* Logout Button */}
											<motion.div
												variants={settingsItemVariants}
												whileHover="hover"
											>
												<Button
													variant="destructive"
													size="sm"
													className="w-full"
													onClick={handleLogout}
												>
													<LogOut className="h-4 w-4 mr-2" />
													{t('auth.logout')}
												</Button>
											</motion.div>
										</Card>
									</motion.div>
								)}

								{/* Settings Sections */}
								<div
									className={`${
										user ? 'lg:col-span-2' : 'lg:col-span-3'
									} space-y-6`}
								>
									{/* Language Section */}
									<motion.div variants={settingsSectionVariants}>
										<Card className="p-4">
											<div className="flex items-center gap-2 mb-4">
												<Languages className="h-5 w-5 text-primary" />
												<span className="text-lg font-semibold">
													{t('settings.language')}
												</span>
											</div>
											<div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
												<motion.div
													variants={settingsItemVariants}
													whileHover="hover"
												>
													<Button
														variant={
															language === Language.EN
																? 'default'
																: 'outline'
														}
														size="sm"
														className="w-full justify-start"
														onClick={() =>
															handleLanguageChange(Language.EN)
														}
													>
														{t(
															getTranslationKeyOfLanguage(Language.EN)
														)}
													</Button>
												</motion.div>
												<motion.div
													variants={settingsItemVariants}
													whileHover="hover"
												>
													<Button
														variant={
															language === Language.VI
																? 'default'
																: 'outline'
														}
														size="sm"
														className="w-full justify-start"
														onClick={() =>
															handleLanguageChange(Language.VI)
														}
													>
														{t(
															getTranslationKeyOfLanguage(Language.VI)
														)}
													</Button>
												</motion.div>
											</div>
										</Card>
									</motion.div>

									{/* Theme Section */}
									<motion.div variants={settingsSectionVariants}>
										<Card className="p-4">
											<div className="flex items-center gap-2 mb-4">
												<Monitor className="h-5 w-5 text-primary" />
												<span className="text-lg font-semibold">
													{t('settings.theme')}
												</span>
											</div>
											<div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
												<motion.div
													variants={settingsItemVariants}
													whileHover="hover"
												>
													<Button
														variant={
															theme === 'light'
																? 'default'
																: 'outline'
														}
														size="sm"
														className="w-full justify-start"
														onClick={() => handleThemeChange('light')}
													>
														<Sun className="h-4 w-4 mr-2" />
														{t('theme.light')}
													</Button>
												</motion.div>
												<motion.div
													variants={settingsItemVariants}
													whileHover="hover"
												>
													<Button
														variant={
															theme === 'dark' ? 'default' : 'outline'
														}
														size="sm"
														className="w-full justify-start"
														onClick={() => handleThemeChange('dark')}
													>
														<Moon className="h-4 w-4 mr-2" />
														{t('theme.dark')}
													</Button>
												</motion.div>
												<motion.div
													variants={settingsItemVariants}
													whileHover="hover"
												>
													<Button
														variant={
															theme === 'system'
																? 'default'
																: 'outline'
														}
														size="sm"
														className="w-full justify-start"
														onClick={() => handleThemeChange('system')}
													>
														<Monitor className="h-4 w-4 mr-2" />
														{t('theme.system')}
													</Button>
												</motion.div>
											</div>
										</Card>
									</motion.div>
								</div>
							</div>
						</Card>
					</motion.div>
				</motion.div>
			</AnimatePresence>

			{/* Change Password Dialog */}
			{showChangePassword && (
				<AnimatePresence>
					<motion.div
						className="fixed inset-0 z-[1400] flex items-center justify-center p-4"
						variants={backdropVariants}
						initial="hidden"
						animate="visible"
						exit="exit"
					>
						<motion.div
							className="absolute inset-0 bg-black/20 backdrop-blur-sm"
							onClick={handleChangePasswordCancel}
						/>
						<motion.div
							variants={settingsPanelVariants}
							initial="hidden"
							animate="visible"
							exit="exit"
							className="relative z-10"
						>
							<ChangePasswordForm
								onSuccess={handleChangePasswordSuccess}
								onCancel={handleChangePasswordCancel}
							/>
						</motion.div>
					</motion.div>
				</AnimatePresence>
			)}
		</>
	);
}
