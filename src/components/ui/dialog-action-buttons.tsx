'use client';

import { Button, Translate } from '@/components/ui';
import { Check, X } from 'lucide-react';

interface DialogActionButtonsProps {
	onCancel: () => void;
	onSubmit: () => void;
	submitDisabled?: boolean;
	isLoading?: boolean;
	cancelTextKey?: string;
	submitTextKey?: string;
}

export function DialogActionButtons({
	onCancel,
	onSubmit,
	submitDisabled = false,
	isLoading = false,
	cancelTextKey = 'ui.cancel',
	submitTextKey = 'ui.save',
}: DialogActionButtonsProps) {
	return (
		<>
			<button
				type="button"
				onClick={onCancel}
				className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
			>
				<X size={16} className="mr-2" />
				<Translate text={cancelTextKey} />
			</button>
			<Button type="submit" disabled={submitDisabled} loading={isLoading} onClick={onSubmit}>
				{!isLoading && <Check size={16} className="mr-2" />}
				<Translate text={submitTextKey} />
			</Button>
		</>
	);
}
