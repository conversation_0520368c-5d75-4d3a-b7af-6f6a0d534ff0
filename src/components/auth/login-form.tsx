'use client';

import {
	<PERSON>ert,
	AlertDescription,
	Button,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { GoogleLoginButton } from './google-login-button';

export function LoginForm() {
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');
	const router = useRouter();
	const { t } = useTranslation();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		try {
			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ username, password }),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || t('auth.login.failed'));
			}

			// Đăng nhập thành công, chuyển hướng về trang chủ
			router.push('/');
			router.refresh();
		} catch (err) {
			setError(err instanceof Error ? err.message : t('auth.login.error'));
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader>
				<CardTitle>
					<Translate text="auth.login.title" />
				</CardTitle>
				<CardDescription>
					<Translate text="auth.login.description" />
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="username">
							<Translate text="auth.login.username_label" />
						</Label>
						<Input
							id="username"
							type="text"
							value={username}
							onChange={(e) => setUsername(e.target.value)}
							placeholder={t('auth.login.username_placeholder')}
							required
							minLength={3}
							disabled={isLoading}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="password">
							<Translate text="auth.login.password_label" />
						</Label>
						<Input
							id="password"
							type="password"
							value={password}
							onChange={(e) => setPassword(e.target.value)}
							placeholder={t('auth.login.password_placeholder')}
							required
							minLength={6}
							disabled={isLoading}
						/>
					</div>
					{error && (
						<Alert variant="destructive">
							<AlertDescription>{error}</AlertDescription>
						</Alert>
					)}
					<Button type="submit" className="w-full" loading={isLoading}>
						<Translate
							text={isLoading ? 'auth.login.logging_in' : 'auth.login.button'}
						/>
					</Button>

					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<span className="w-full border-t" />
						</div>
						<div className="relative flex justify-center text-xs uppercase">
							<span className="bg-background px-2 text-muted-foreground">
								<Translate text="auth.login.or" />
							</span>
						</div>
					</div>

					<GoogleLoginButton
						onSuccess={() => {
							router.push('/');
							router.refresh();
						}}
						onError={(error) => setError(error)}
						disabled={isLoading}
					/>
				</form>
			</CardContent>
		</Card>
	);
}
