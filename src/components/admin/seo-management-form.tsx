'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/contexts/toast-context';
import { SeoSettings } from '@prisma/client';
import { RotateCcw, Save, Eye } from 'lucide-react';
import { useCallback, useState } from 'react';

interface SeoManagementFormProps {
	settings: SeoSettings;
	onUpdate: (settings: SeoSettings) => void;
	isLoading?: boolean;
}

export function SeoManagementForm({
	settings,
	onUpdate,
	isLoading = false,
}: SeoManagementFormProps) {
	const [formData, setFormData] = useState<Partial<SeoSettings>>(settings);

	const { showSuccess, showError } = useToast();

	const handleInputChange = useCallback((field: keyof SeoSettings, value: string | null) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	}, []);

	const handleSave = useCallback(async () => {
		try {
			const response = await fetch('/api/admin/seo', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formData),
				credentials: 'include',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update SEO settings');
			}

			const updatedSettings = await response.json();
			onUpdate(updatedSettings);
			showSuccess('SEO settings updated successfully');
		} catch (error) {
			console.error('Failed to update SEO settings:', error);
			showError(error instanceof Error ? error.message : 'Failed to update SEO settings');
		}
	}, [formData, onUpdate, showSuccess, showError]);

	const handleReset = useCallback(async () => {
		if (
			!confirm(
				'Are you sure you want to reset all SEO settings to defaults? This action cannot be undone.'
			)
		) {
			return;
		}

		try {
			const response = await fetch('/api/admin/seo/reset', {
				method: 'POST',
				credentials: 'include',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to reset SEO settings');
			}

			const defaultSettings = await response.json();
			setFormData(defaultSettings);
			onUpdate(defaultSettings);
			showSuccess('SEO settings reset to defaults');
		} catch (error) {
			console.error('Failed to reset SEO settings:', error);
			showError(error instanceof Error ? error.message : 'Failed to reset SEO settings');
		}
	}, [onUpdate, showSuccess, showError]);

	return (
		<div className="space-y-6">
			{/* Basic SEO Settings */}
			<Card>
				<CardHeader>
					<CardTitle>Basic SEO Settings</CardTitle>
					<CardDescription>
						Configure the basic SEO metadata for your application
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="title">Title</Label>
						<Input
							id="title"
							value={formData.title || ''}
							onChange={(e) => handleInputChange('title', e.target.value)}
							placeholder="Enter page title"
							maxLength={60}
						/>
						<p className="text-sm text-muted-foreground">
							{(formData.title || '').length}/60 characters
						</p>
					</div>

					<div className="space-y-2">
						<Label htmlFor="description">Description</Label>
						<Textarea
							id="description"
							value={formData.description || ''}
							onChange={(e) => handleInputChange('description', e.target.value)}
							placeholder="Enter meta description"
							maxLength={160}
							rows={3}
						/>
						<p className="text-sm text-muted-foreground">
							{(formData.description || '').length}/160 characters
						</p>
					</div>

					<div className="space-y-2">
						<Label htmlFor="keywords">Keywords</Label>
						<Input
							id="keywords"
							value={formData.keywords || ''}
							onChange={(e) => handleInputChange('keywords', e.target.value)}
							placeholder="Enter keywords separated by commas"
							maxLength={255}
						/>
						<p className="text-sm text-muted-foreground">
							{(formData.keywords || '').length}/255 characters
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="language">Language</Label>
							<Input
								id="language"
								value={formData.language || ''}
								onChange={(e) => handleInputChange('language', e.target.value)}
								placeholder="e.g., en, en-US"
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="robots">Robots</Label>
							<Select
								value={formData.robots || 'index, follow'}
								onValueChange={(value) => handleInputChange('robots', value)}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="index, follow">Index, Follow</SelectItem>
									<SelectItem value="noindex, nofollow">
										No Index, No Follow
									</SelectItem>
									<SelectItem value="index, nofollow">
										Index, No Follow
									</SelectItem>
									<SelectItem value="noindex, follow">
										No Index, Follow
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Open Graph & Twitter Settings */}
			<Card>
				<CardHeader>
					<CardTitle>Social Media & Open Graph</CardTitle>
					<CardDescription>
						Configure how your site appears when shared on social media
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="og_title">Open Graph Title</Label>
							<Input
								id="og_title"
								value={formData.og_title || ''}
								onChange={(e) =>
									handleInputChange('og_title', e.target.value || null)
								}
								placeholder="Leave empty to use main title"
								maxLength={60}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="twitter_card">Twitter Card Type</Label>
							<Select
								value={formData.twitter_card || 'summary_large_image'}
								onValueChange={(value) => handleInputChange('twitter_card', value)}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="summary">Summary</SelectItem>
									<SelectItem value="summary_large_image">
										Summary Large Image
									</SelectItem>
									<SelectItem value="app">App</SelectItem>
									<SelectItem value="player">Player</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="og_description">Open Graph Description</Label>
						<Textarea
							id="og_description"
							value={formData.og_description || ''}
							onChange={(e) =>
								handleInputChange('og_description', e.target.value || null)
							}
							placeholder="Leave empty to use main description"
							maxLength={160}
							rows={2}
						/>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="twitter_site">Twitter Site Handle</Label>
							<Input
								id="twitter_site"
								value={formData.twitter_site || ''}
								onChange={(e) =>
									handleInputChange('twitter_site', e.target.value || null)
								}
								placeholder="@yoursite"
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="twitter_creator">Twitter Creator Handle</Label>
							<Input
								id="twitter_creator"
								value={formData.twitter_creator || ''}
								onChange={(e) =>
									handleInputChange('twitter_creator', e.target.value || null)
								}
								placeholder="@creator"
							/>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="og_image_url">Open Graph Image URL</Label>
						<Input
							id="og_image_url"
							value={formData.og_image_url || ''}
							onChange={(e) =>
								handleInputChange('og_image_url', e.target.value || null)
							}
							placeholder="https://example.com/image.jpg"
						/>
					</div>
				</CardContent>
			</Card>

			{/* Advanced Settings */}
			<Card>
				<CardHeader>
					<CardTitle>Advanced Settings</CardTitle>
					<CardDescription>Additional SEO and technical settings</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="canonical_url">Canonical URL</Label>
						<Input
							id="canonical_url"
							value={formData.canonical_url || ''}
							onChange={(e) =>
								handleInputChange('canonical_url', e.target.value || null)
							}
							placeholder="https://yourdomain.com"
						/>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="theme_color">Theme Color</Label>
							<div className="flex space-x-2">
								<Input
									id="theme_color"
									value={formData.theme_color || ''}
									onChange={(e) =>
										handleInputChange('theme_color', e.target.value)
									}
									placeholder="#000000"
								/>
								<input
									type="color"
									value={formData.theme_color || '#000000'}
									onChange={(e) =>
										handleInputChange('theme_color', e.target.value)
									}
									className="w-12 h-10 border border-gray-300 rounded"
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="background_color">Background Color</Label>
							<div className="flex space-x-2">
								<Input
									id="background_color"
									value={formData.background_color || ''}
									onChange={(e) =>
										handleInputChange('background_color', e.target.value)
									}
									placeholder="#ffffff"
								/>
								<input
									type="color"
									value={formData.background_color || '#ffffff'}
									onChange={(e) =>
										handleInputChange('background_color', e.target.value)
									}
									className="w-12 h-10 border border-gray-300 rounded"
								/>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Action Buttons */}
			<div className="flex justify-between">
				<Button variant="outline" onClick={handleReset} disabled={isLoading}>
					<RotateCcw className="w-4 h-4 mr-2" />
					Reset to Defaults
				</Button>

				<div className="space-x-2">
					<Button variant="outline" onClick={() => window.open('/', '_blank')}>
						<Eye className="w-4 h-4 mr-2" />
						Preview
					</Button>
					<Button onClick={handleSave} disabled={isLoading}>
						<Save className="w-4 h-4 mr-2" />
						{isLoading ? 'Saving...' : 'Save Changes'}
					</Button>
				</div>
			</div>
		</div>
	);
}
