'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Input,
	Label,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { Eye, EyeOff, Key, AlertTriangle } from 'lucide-react';

interface ChangeUserPasswordFormProps {
	userId: string;
	username: string;
	onSuccess?: () => void;
	onCancel?: () => void;
}

export function ChangeUserPasswordForm({
	userId,
	username,
	onSuccess,
	onCancel,
}: ChangeUserPasswordFormProps) {
	const { showSuccess, showError } = useToast();

	const [newPassword, setNewPassword] = useState('');
	const [confirmPassword, setConfirmPassword] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');

	// Password visibility states
	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		// Client-side validation
		if (newPassword.length < 6) {
			setError('New password must be at least 6 characters long');
			setIsLoading(false);
			return;
		}

		if (newPassword !== confirmPassword) {
			setError('Passwords do not match');
			setIsLoading(false);
			return;
		}

		try {
			const response = await fetch(`/api/admin/users/${userId}/change-password`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					newPassword,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || 'Failed to change user password');
			}

			// Success
			showSuccess(`Password changed successfully for user ${username}`);

			// Reset form
			setNewPassword('');
			setConfirmPassword('');

			// Call success callback
			onSuccess?.();
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to change user password';
			setError(errorMessage);
			showError(errorMessage);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Card className="w-full max-w-md">
			<CardHeader className="text-center space-y-2">
				<div className="flex justify-center">
					<div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
						<Key className="h-5 w-5 text-primary" />
					</div>
				</div>
				<CardTitle className="text-xl">Change User Password</CardTitle>
				<CardDescription>
					Change password for user: <strong>{username}</strong>
				</CardDescription>
			</CardHeader>

			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* New Password */}
					<div className="space-y-2">
						<Label htmlFor="new-password">New Password</Label>
						<div className="relative">
							<Input
								id="new-password"
								type={showNewPassword ? 'text' : 'password'}
								value={newPassword}
								onChange={(e) => setNewPassword(e.target.value)}
								placeholder="Enter new password (minimum 6 characters)"
								required
								disabled={isLoading}
								className="pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
								onClick={() => setShowNewPassword(!showNewPassword)}
								disabled={isLoading}
							>
								{showNewPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>

					{/* Confirm Password */}
					<div className="space-y-2">
						<Label htmlFor="confirm-password">Confirm New Password</Label>
						<div className="relative">
							<Input
								id="confirm-password"
								type={showConfirmPassword ? 'text' : 'password'}
								value={confirmPassword}
								onChange={(e) => setConfirmPassword(e.target.value)}
								placeholder="Confirm new password"
								required
								disabled={isLoading}
								className="pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
								onClick={() => setShowConfirmPassword(!showConfirmPassword)}
								disabled={isLoading}
							>
								{showConfirmPassword ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>

					{/* Error Message */}
					{error && (
						<div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg flex items-start space-x-2">
							<AlertTriangle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
							<p className="text-sm text-destructive">{error}</p>
						</div>
					)}

					{/* Action Buttons */}
					<div className="flex gap-2 pt-2">
						{onCancel && (
							<Button
								type="button"
								variant="outline"
								onClick={onCancel}
								disabled={isLoading}
								className="flex-1"
							>
								Cancel
							</Button>
						)}
						<Button type="submit" loading={isLoading} className="flex-1">
							{!isLoading && <Key className="h-4 w-4 mr-2" />}
							{isLoading ? 'Changing...' : 'Change Password'}
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
