# Keyword System Redesign

## Overview

The keyword system has been redesigned to provide a better user experience by:

-   Eliminating loading states for user actions
-   Using localStorage for immediate data persistence
-   Implementing background synchronization with the server
-   Providing optimistic updates for all operations

## Architecture

### Components

1. **KeywordStorage** (`src/lib/keyword-storage.ts`)

    - Manages localStorage operations
    - Handles optimistic updates
    - Manages sync queue for background operations

2. **useKeywordSync** (`src/hooks/use-keyword-sync.ts`)

    - Handles background synchronization with server
    - Processes sync queue automatically
    - Provides sync status and manual sync capabilities

3. **KeywordsContext** (`src/contexts/keywords-context.tsx`)

    - Updated to use localStorage as primary data source
    - Implements optimistic updates for all operations
    - Integrates with background sync system

4. **KeywordForm** (`src/app/collections/[id]/components/keyword-form.tsx`)
    - Removed loading states for better UX
    - Shows sync status indicator
    - Immediate response to user actions

## Key Features

### 1. Optimistic Updates

All keyword operations (create, update, delete) are applied immediately to localStorage, providing instant feedback to users.

### 2. Background Synchronization

-   Automatic sync every 5 seconds
-   Sync on tab focus/visibility change
-   Manual sync capability
-   Retry mechanism for failed operations

### 3. Offline Support

-   Works offline with localStorage
-   Queues operations for sync when online
-   Graceful degradation when server is unavailable

### 4. Sync Status Indicators

-   Visual indicators for pending sync operations
-   Sync progress feedback
-   Success indicators that auto-hide after 2 seconds
-   Error handling and reporting

## Usage

### Basic Operations

```typescript
const {
	keywords,
	selectedKeywords,
	setSelectedKeywords,
	createKeyword,
	updateKeyword,
	deleteKeyword,
	syncStatus,
	syncNow,
} = useKeywordsContext();

// Create keyword (immediate local update + background sync)
const newKeyword = await createKeyword('technology');

// Update keyword (immediate local update + background sync)
await updateKeyword(keywordId, 'updated-name');

// Delete keyword (immediate local update + background sync)
await deleteKeyword(keywordId);

// Check sync status
console.log(syncStatus.pendingActions); // Number of pending sync operations
console.log(syncStatus.hasUnsyncedChanges); // Boolean

// Force sync
await syncNow();
```

### Sync Status

```typescript
const { syncStatus } = useKeywordsContext();

// Check if there are pending operations
if (syncStatus.hasUnsyncedChanges) {
	// Show "syncing" indicator
}

// Check if sync just completed successfully
if (syncStatus.showSyncSuccess) {
	// Show "synced successfully" indicator (auto-hides after 2 seconds)
}

// Number of pending operations
const pendingCount = syncStatus.pendingActions;

// Complete sync status object
const status = {
	pendingActions: number, // Number of pending sync operations
	isSync: boolean, // Currently syncing
	hasUnsyncedChanges: boolean, // Has pending changes
	showSyncSuccess: boolean, // Show success indicator (auto-hides)
};
```

## Data Flow

1. **User Action** → Immediate localStorage update → UI update
2. **Background Process** → Sync queue processing → Server API call
3. **Success** → Remove from sync queue
4. **Failure** → Keep in queue for retry

## Storage Structure

### Keywords Storage

```typescript
{
  keywords: KeywordWithDetail[],
  lastSyncTimestamp: number,
  version: number
}
```

### Selected Keywords Storage

```typescript
string[] // Array of keyword IDs
```

### Sync Queue Storage

```typescript
{
  id: string,
  type: 'create' | 'update' | 'delete',
  data: {
    name?: string,
    keywordId?: string
  },
  timestamp: number
}[]
```

## Configuration

### Sync Interval

Default: 5 seconds
Can be configured in `useKeywordSync` hook:

```typescript
const { syncNow } = useKeywordSync({
	syncInterval: 10000, // 10 seconds
	hideSuccessDelay: 3000, // 3 seconds to hide success indicator
	onSyncSuccess: (action) => console.log('Synced:', action),
	onSyncError: (action, error) => console.error('Sync failed:', action, error),
});
```

### Storage Keys

-   `vocab-keywords`: Main keywords data
-   `vocab-selected-keywords`: Selected keyword IDs
-   `vocab-keywords-sync-queue`: Pending sync operations

## Error Handling

### Sync Errors

-   Failed operations remain in sync queue
-   Automatic retry on next sync cycle
-   Error callbacks for custom handling

### Storage Errors

-   Graceful fallback to empty arrays
-   Console error logging
-   No data loss on storage failures

## Testing

### Test Pages

Visit these pages to test the keyword system:

**Main Test Page**: `/test-sync`

-   Test sync status indicators
-   Watch success indicators auto-hide
-   Create/delete keywords to see status changes
-   Visual feedback for all sync states

**Collections Page**: `/collections/[id]`

-   Real keyword form with sync indicators
-   Production-ready implementation
-   Integrated with collection workflow

### Unit Tests

Run keyword system tests:

```bash
yarn test src/test/keyword-system.test.ts
```

## Migration

The new system is backward compatible:

-   Existing keywords are loaded from server on first visit
-   localStorage is populated with server data
-   Subsequent operations use localStorage + background sync

## Benefits

1. **Better UX**: No loading states for user actions
2. **Offline Support**: Works without internet connection
3. **Performance**: Faster response times
4. **Reliability**: Automatic retry for failed operations
5. **Transparency**: Clear sync status indicators

## Future Enhancements

1. **Conflict Resolution**: Handle concurrent edits from multiple devices
2. **Batch Operations**: Optimize multiple operations
3. **Smart Sync**: Sync only changed data
4. **Compression**: Reduce localStorage usage
5. **Analytics**: Track sync performance and errors
