# WordNet Scripts

This directory contains a single comprehensive script for downloading, setting up, and managing WordNet data integration in the vocabulary application.

## Quick Start

### One-Command Setup (Recommended)

```bash
# Quick setup with 1000 common words (10-15 minutes)
yarn wordnet:quick

# Or full setup with all WordNet data (2-4 hours)
yarn wordnet:full
```

### Manual Step-by-Step

```bash
# 1. Install dependencies
yarn wordnet:install-deps

# 2. Download WordNet files
yarn wordnet:download

# 3. Load data into database
yarn wordnet:load --pos=noun --max-words=1000

# 4. Test the setup
yarn wordnet:test
```

## Available Scripts

### Main Commands

-   `yarn wordnet` - Complete automated setup (quick mode)
-   `yarn wordnet:quick` - Quick setup (1000 words)
-   `yarn wordnet:full` - Full setup (~155k words)
-   `yarn wordnet:test` - Test existing setup
-   `yarn wordnet:stats` - Show current statistics

### Individual Components

-   `yarn wordnet:install-deps` - Install required dependencies
-   `yarn wordnet:download` - Download WordNet files
-   `yarn wordnet:load` - Load data into database

## Script File

### Core Script

-   `wordnet.ts` - Single comprehensive script containing all WordNet functionality

### Features

-   **All-in-one solution** - Single script handles everything
-   **Automated downloads** from Princeton University
-   **Intelligent parsing** of WordNet database files
-   **Batch processing** for optimal performance
-   **Progress tracking** and statistics
-   **Error handling** and recovery
-   **Comprehensive testing** and verification

## Usage Examples

### Development Setup

```bash
# Quick start for development
yarn wordnet:quick

# Check what was loaded
yarn wordnet:stats

# Test functionality
yarn wordnet:test
```

### Production Setup

```bash
# Full WordNet database
yarn wordnet:full

# Monitor progress
yarn wordnet:stats

# Verify everything works
yarn wordnet:test
```

### Custom Loading

```bash
# Load specific amounts
yarn wordnet:load --pos=noun --max-words=5000
yarn wordnet:load --pos=verb --max-words=1000

# Test without saving
yarn wordnet:load --pos=noun --max-words=100 --dry-run
```

### Troubleshooting

```bash
# Check current status
yarn wordnet:stats

# Test individual components
yarn wordnet:test --verbose

# Force re-download
yarn wordnet:download --force

# Force reload data
yarn wordnet:load --force


## File Structure

After running the scripts, WordNet data is organized as:

```

data/
└── wordnet/
├── downloads/ # Temporary files (auto-cleaned)
└── extracted/ # WordNet database files
├── data.noun # Noun synset data
├── data.verb # Verb synset data
├── data.adj # Adjective synset data
├── data.adv # Adverb synset data
├── index.noun # Noun index
├── index.verb # Verb index
├── index.adj # Adjective index
├── index.adv # Adverb index
└── \*.exc # Exception lists

````

## Performance

### Loading Times

-   **Dependencies**: 2-5 minutes
-   **Download**: 2-5 minutes
-   **Quick setup**: 5-10 minutes (1000 words)
-   **Full setup**: 2-4 hours (155k words)

### Database Impact

-   **Quick setup**: ~50 MB database size
-   **Full setup**: ~500 MB - 1 GB database size
-   **Query performance**: <100ms for typical lookups

## Requirements

### System Requirements

-   Node.js 18+ with TypeScript support
-   PostgreSQL database
-   Internet connection for downloads
-   ~1 GB free disk space for full setup

### Dependencies (Auto-installed)

-   `unzipper` - File extraction
-   `natural` - Natural language processing
-   `@prisma/client` - Database access
-   TypeScript types for all packages

## Troubleshooting

### Common Issues

1. **Download fails**: Check internet connection, try `--force`
2. **Database errors**: Ensure PostgreSQL is running, check `DATABASE_URL`
3. **Permission errors**: Check file/directory permissions
4. **Memory issues**: Use smaller batch sizes, load one POS at a time

### Getting Help

1. Run `yarn wordnet:test --verbose` for diagnostics
2. Check `docs/WORDNET_SETUP_GUIDE.md` for detailed guide
3. Review console output for specific error messages
4. Use `--dry-run` to test without making changes

## Integration

Once setup is complete, WordNet data is automatically available in your application:

```typescript
// Search words with WordNet data
const words = await wordService.searchWordsWithWordNet('house');

// Get WordNet information
const wordNetInfo = await wordNetService.getWordNetInfo('house', Language.EN);

// Display in UI
<WordNetInfo wordNetData={word.wordnet_data} term={word.term} />;
````

## Maintenance

### Regular Tasks

-   Monitor database size and performance
-   Update WordNet data as needed
-   Run tests after system updates
-   Backup WordNet data before major changes

### Useful Commands

```bash
# Check current statistics
yarn wordnet:stats

# Test functionality
yarn wordnet:test

# Add more data
yarn wordnet:load --pos=verb --max-words=2000

# Verify integrity
yarn wordnet:test
```

For detailed documentation, see `docs/WORDNET_SETUP_GUIDE.md`.
