# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Essential Commands

**Development:**
- `yarn dev` - Start development server on port 6001 with Turbopack
- `yarn dup` - Start local databases (PostgreSQL + MongoDB)
- `yarn p:m` - Run Prisma migrations
- `yarn p:s` - Open Prisma Studio
- `yarn build` - Production build (includes Prisma migration)

**Testing:**
- `yarn test` - Run Jest unit tests
- `yarn test:unit` - Run Vitest tests
- `yarn test:e2e` - Run Playwright E2E tests
- `yarn test:all` - Run all test suites

**Linting:**
- `yarn lint` - Run all linting (oxlint + eslint + TypeScript)
- `yarn lint:fix` - Auto-fix linting issues

**Critical:** Always use **Yarn v4.9.2**, never npm. Application runs on localhost:6001, not 3000.

## 🏗️ Architecture Overview

This is a Next.js 15 AI-powered vocabulary learning app using **Domain-Driven Design (DDD)** with clean architecture:

### Core Layers
1. **API Layer** (`src/app/api/`) - Next.js API routes with authentication & validation
2. **Service Layer** (`src/backend/services/`) - Business logic (21 services)
3. **Repository Layer** (`src/backend/repositories/`) - Data access with Prisma (9 repositories)
4. **Wire Layer** (`src/backend/wire.ts`) - Dependency injection container

### Key Technologies
- **Frontend:** Next.js 15, TypeScript (strict), Tailwind CSS v4, Radix UI
- **Backend:** PostgreSQL + Prisma ORM, OpenAI GPT-4o-mini, Google Gemini
- **State:** React Context API + Custom hooks
- **Auth:** JWT with Telegram/Google/Username providers
- **Caching:** Multi-tier (Node-cache, Redis, semantic caching)
- **Testing:** Jest + Vitest + Playwright + MSW

## 📁 Critical File Structure

```
src/
├── app/                    # Next.js App Router (pages + API routes)
├── backend/               # Server-side architecture
│   ├── wire.ts           # 🔑 Dependency injection (START HERE)
│   ├── services/         # Business logic
│   ├── repositories/     # Data access  
│   └── middleware/       # Auth & validation
├── components/           # UI components (50+ components)
├── contexts/            # React contexts (11 contexts)
│   └── translations/    # i18n files (25+ languages)
├── hooks/               # Custom React hooks
└── lib/                # Utilities & shared logic
```

## 🔑 Dependency Injection Pattern

**Central to this architecture:** All services/repositories are wired through `src/backend/wire.ts`

```typescript
// ✅ Correct way to get services in API routes
import { getCollectionService } from '@/backend/wire';

// ❌ Never import services/repositories directly
import { CollectionService } from '@/backend/services/collection.service';
```

Key getters: `getCollectionService()`, `getUserService()`, `getLLMService()`, `getWordService()`

## 🎯 Development Patterns

### File Naming
- **Components:** PascalCase (`UserProfile.tsx`)
- **Backend files:** kebab-case (`collection.service.ts`, `auth.middleware.ts`)
- **Hooks:** camelCase with `use` prefix (`useCollections.ts`)
- **API routes:** Next.js convention (`route.ts`)

### Code Conventions
- Use `'use server'` for API routes
- Use `'use client'` for client components/contexts
- Import local files with `@/` alias
- Named exports only (no default exports in most cases)
- TypeScript strict mode everywhere

### Error Handling
```typescript
// Use custom error classes from @/backend/errors
import { ValidationError, NotFoundError, UnauthorizedError } from '@/backend/errors';
```

## 🌐 Internationalization

Translation system in `src/contexts/translations/`:
- Supports English (EN) and Vietnamese (VI)
- Structured keys: `nav.home`, `collections.create`
- Always update translation context when adding new keys
- Use `useTranslation` hook from translation context

## 🔐 Security & Authentication

- JWT tokens in HttpOnly cookies
- Multiple auth providers (Telegram, Google, Username/Password)
- Comprehensive middleware stack with rate limiting
- Zod schema validation for all API inputs
- CSRF protection with double-submit pattern

## 🗄️ Database Architecture

**PostgreSQL with Prisma ORM:**
- UUID primary keys throughout
- Complex relationships: User → Collections → Words → Paragraphs
- Bilingual content support (EN/VI)
- Soft references for flexibility
- Strategic indexing for performance

Key models: User, Collection, Word, Definition, Paragraph, Keyword, LastSeenWord

Migration workflow:
1. Modify `prisma/schema.prisma`
2. Run `yarn p:m` to generate migration
3. Review migration before applying to production

## 🧪 AI Integration

**Multi-LLM architecture:**
- Primary: OpenAI GPT-4o-mini
- Secondary: Google Gemini (via Genkit)
- Advanced caching with TTL strategies
- Token monitoring and cost optimization
- Adaptive model selection based on complexity

LLM features: vocabulary generation, paragraph creation, Q&A generation, grammar practice

## 🎨 UI System

**Comprehensive design system:**
- 50+ reusable UI components in `src/components/ui/`
- Tailwind CSS v4 with custom design tokens
- Dark/light theme support
- Framer Motion animations
- Accessibility-first with ARIA support
- PWA capabilities with offline support

## 🧪 Testing Strategy

**Multi-layer testing:**
- **Unit:** Jest + Vitest (70% coverage target)
- **Integration:** API + service layer testing
- **E2E:** Playwright (Chrome, Firefox, Safari)
- **Performance:** Load testing for critical paths

Run `yarn test:setup` for initial test environment setup.

## 🚀 Deployment

**Vercel with automatic CI/CD:**
- Auto-deploy from main branch
- Build includes automatic Prisma migration
- Environment variables in Vercel dashboard
- Region: Singapore (sin1) for optimal performance

## 💡 Common Development Tasks

**Adding new API endpoint:**
1. Create in `src/app/api/[feature]/route.ts`
2. Use services via `src/backend/wire.ts`
3. Add proper authentication & validation
4. Update tests

**Adding new service:**
1. Create interface + implementation in `src/backend/services/`
2. Add to dependency injection in `wire.ts`
3. Follow existing service patterns

**Adding translations:**
1. Update files in `src/contexts/translations/`
2. Always update both EN and VI
3. Use structured key naming

**Database changes:**
1. Modify `prisma/schema.prisma`
2. Run `yarn p:m` with descriptive name
3. Test migration locally first

## ⚠️ Important Notes

- **Test locally at `localhost:6001`** (not 3000)
- **Always use Yarn**, never npm
- **Follow DDD principles** - services call repositories, APIs call services
- **Update translations** when adding new UI text
- **Run linting** before commits: `yarn lint`
- **Migration safety** - always backup before production deploys

This project emphasizes clean architecture, type safety, comprehensive testing, and excellent developer experience.