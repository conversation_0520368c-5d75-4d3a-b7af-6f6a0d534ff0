import axios from 'axios';
import cheerio from 'cheerio';
import fs from 'fs-extra';
import createCsvWriter from 'csv-writer';

const CSV_WRITER = createCsvWriter.createObjectCsvWriter({
  path: './data/english_profile_words.csv',
  header: [
    { id: 'word', title: 'word' },
    { id: 'cefr_level', title: 'cefr_level' },
    { id: 'frequency', title: 'frequency' },
    { id: 'source', title: 'source' },
    { id: 'pos', title: 'pos' }
  ]
});

class EnglishProfileScraper {
  constructor() {
    this.baseUrl = 'https://englishprofile.org';
    this.words = [];
    this.delay = 1000; // 1 second delay between requests
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async scrapeWordList(level) {
    try {
      console.log(`Scraping CEFR level ${level}...`);
      
      // Note: This URL structure is hypothetical - English Profile may require authentication
      // or have different access patterns. This serves as a template.
      const url = `${this.baseUrl}/wordlist/${level.toLowerCase()}`;
      
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      
      // Extract words from the page structure
      // This selector needs to be adjusted based on actual HTML structure
      const wordElements = $('.word-entry, .vocabulary-item, .word-list-item');
      
      wordElements.each((index, element) => {
        const word = $(element).find('.word, .headword').text().trim();
        const pos = $(element).find('.pos, .part-of-speech').text().trim();
        const frequency = $(element).find('.frequency').text().trim() || '';
        
        if (word) {
          this.words.push({
            word: word.toLowerCase(),
            cefr_level: level,
            frequency: frequency || '',
            source: 'english_profile',
            pos: pos || ''
          });
        }
      });

      console.log(`Found ${wordElements.length} words for level ${level}`);
      await this.sleep(this.delay);
      
    } catch (error) {
      console.error(`Error scraping level ${level}:`, error.message);
      
      // Fallback: try to get data from vocabulary list pages
      await this.tryAlternativeMethod(level);
    }
  }

  async tryAlternativeMethod(level) {
    try {
      // Alternative approach: try to access vocabulary lists through search
      const searchUrl = `${this.baseUrl}/search?q=level:${level}`;
      const response = await axios.get(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });

      const $ = cheerio.load(response.data);
      
      // Extract words from search results
      $('.search-result .word, .result-word').each((index, element) => {
        const word = $(element).text().trim();
        if (word) {
          this.words.push({
            word: word.toLowerCase(),
            cefr_level: level,
            frequency: '',
            source: 'english_profile_search',
            pos: ''
          });
        }
      });
      
    } catch (error) {
      console.error(`Alternative method failed for level ${level}:`, error.message);
    }
  }

  async scrapeSampleWords() {
    // Since English Profile may require authentication, provide sample data
    // based on known CEFR vocabulary distributions
    console.log('Using sample CEFR vocabulary data...');
    
    const sampleWords = {
      'A1': ['be', 'have', 'do', 'say', 'get', 'make', 'go', 'know', 'take', 'see', 'come', 'think', 'look', 'want', 'give', 'use', 'find', 'tell', 'ask', 'work', 'seem', 'feel', 'try', 'leave', 'call'],
      'A2': ['become', 'put', 'mean', 'keep', 'let', 'begin', 'seem', 'help', 'talk', 'turn', 'start', 'might', 'show', 'hear', 'play', 'run', 'move', 'live', 'believe', 'hold', 'bring', 'happen', 'write', 'provide', 'sit'],
      'B1': ['include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'love', 'consider', 'appear'],
      'B2': ['suggest', 'require', 'develop', 'carry', 'break', 'receive', 'decide', 'build', 'reach', 'kill', 'remain', 'return', 'travel', 'affect', 'hit', 'produce', 'eat', 'cover', 'catch', 'draw', 'choose', 'die', 'expect', 'stay', 'result'],
      'C1': ['establish', 'achieve', 'maintain', 'involve', 'ensure', 'determine', 'acquire', 'obtain', 'regard', 'assess', 'indicate', 'concentrate', 'constitute', 'conduct', 'pursue', 'encounter', 'demonstrate', 'implement', 'perceive', 'eliminate', 'emphasize', 'emerge', 'facilitate', 'generate', 'initiate'],
      'C2': ['contemplate', 'endeavour', 'substantiate', 'exemplify', 'assimilate', 'corroborate', 'conceptualize', 'synthesize', 'hypothesize', 'extrapolate', 'perpetuate', 'substantiate', 'consolidate', 'articulate', 'predispose', 'amalgamate', 'differentiate', 'accommodate', 'deliberate', 'enumerate', 'propagate', 'capitalize', 'complement', 'supplement', 'deteriorate']
    };

    for (const [level, words] of Object.entries(sampleWords)) {
      words.forEach((word, index) => {
        this.words.push({
          word: word.toLowerCase(),
          cefr_level: level,
          frequency: (index + 1).toString(),
          source: 'english_profile_sample',
          pos: ''
        });
      });
    }
  }

  async scrapeAll() {
    await fs.ensureDir('./data');
    
    const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    
    try {
      // Try to scrape from actual website
      for (const level of levels) {
        await this.scrapeWordList(level);
      }
      
      // If no words were found, use sample data
      if (this.words.length === 0) {
        await this.scrapeSampleWords();
      }
      
    } catch (error) {
      console.error('Primary scraping failed, using sample data:', error.message);
      await this.scrapeSampleWords();
    }

    // Write to CSV
    await CSV_WRITER.writeRecords(this.words);
    console.log(`Saved ${this.words.length} words to english_profile_words.csv`);
    
    return this.words;
  }
}

// Run the scraper
if (import.meta.url === `file://${process.argv[1]}`) {
  const scraper = new EnglishProfileScraper();
  scraper.scrapeAll()
    .then(() => console.log('English Profile scraping completed'))
    .catch(console.error);
}

export default EnglishProfileScraper;