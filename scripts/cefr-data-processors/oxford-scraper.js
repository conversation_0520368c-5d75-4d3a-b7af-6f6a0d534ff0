import axios from 'axios';
import cheerio from 'cheerio';
import fs from 'fs-extra';
import createCsvWriter from 'csv-writer';

const CSV_WRITER = createCsvWriter.createObjectCsvWriter({
  path: './data/oxford_words.csv',
  header: [
    { id: 'word', title: 'word' },
    { id: 'cefr_level', title: 'cefr_level' },
    { id: 'frequency', title: 'frequency' },
    { id: 'source', title: 'source' },
    { id: 'pos', title: 'pos' }
  ]
});

class OxfordScraper {
  constructor() {
    this.baseUrl = 'https://www.oxfordlearnersdictionaries.com';
    this.words = [];
    this.delay = 2000; // 2 second delay between requests
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async scrapeWordlist() {
    try {
      console.log('Scraping Oxford Learner\'s Dictionary wordlist...');
      
      // Try to access the wordlist page
      const wordlistUrl = `${this.baseUrl}/wordlist`;
      const response = await axios.get(wordlistUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive'
        },
        timeout: 15000
      });

      const $ = cheerio.load(response.data);
      
      // Look for CEFR level sections or word lists
      await this.extractWordsFromPage($);
      await this.sleep(this.delay);
      
      // Try to find links to individual level pages
      const levelLinks = $('a[href*="cefr"], a[href*="level"], a[href*="a1"], a[href*="a2"], a[href*="b1"], a[href*="b2"], a[href*="c1"], a[href*="c2"]');
      
      for (let i = 0; i < Math.min(levelLinks.length, 6); i++) {
        const link = levelLinks.eq(i);
        const href = link.attr('href');
        
        if (href) {
          const fullUrl = href.startsWith('http') ? href : `${this.baseUrl}${href}`;
          await this.scrapeLevelPage(fullUrl);
          await this.sleep(this.delay);
        }
      }
      
    } catch (error) {
      console.error('Error scraping Oxford wordlist:', error.message);
      console.log('Using sample Oxford CEFR data...');
      await this.useSampleOxfordData();
    }
  }

  async scrapeLevelPage(url) {
    try {
      console.log(`Scraping level page: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      await this.extractWordsFromPage($, this.detectLevelFromUrl(url));
      
    } catch (error) {
      console.error(`Error scraping ${url}:`, error.message);
    }
  }

  detectLevelFromUrl(url) {
    const urlLower = url.toLowerCase();
    if (urlLower.includes('a1')) return 'A1';
    if (urlLower.includes('a2')) return 'A2';
    if (urlLower.includes('b1')) return 'B1';
    if (urlLower.includes('b2')) return 'B2';
    if (urlLower.includes('c1')) return 'C1';
    if (urlLower.includes('c2')) return 'C2';
    return '';
  }

  async extractWordsFromPage($, defaultLevel = '') {
    // Try multiple selectors for word extraction
    const selectors = [
      '.wordlist-item .headword',
      '.word-entry .headword',
      '.vocabulary-item .word',
      '.wordlist .word',
      '.entry-body .headword',
      '.top-g .headword',
      'h2.headword',
      '.headword'
    ];

    let wordsFound = 0;
    
    for (const selector of selectors) {
      const elements = $(selector);
      
      if (elements.length > 0) {
        console.log(`Using selector: ${selector}, found ${elements.length} elements`);
        
        elements.each((index, element) => {
          const $el = $(element);
          const word = $el.text().trim();
          
          // Try to detect CEFR level from nearby elements
          let level = defaultLevel;
          if (!level) {
            const parent = $el.closest('.wordlist-item, .word-entry, .entry-body');
            const levelText = parent.find('.cefr, .level, .difficulty').text();
            level = this.extractCEFRLevel(levelText) || this.detectLevelFromContext($el);
          }
          
          // Try to get part of speech
          const pos = $el.closest('.entry-body, .word-entry').find('.pos').first().text().trim();
          
          if (word && word.match(/^[a-zA-Z]+$/)) {
            this.words.push({
              word: word.toLowerCase(),
              cefr_level: level || 'B1', // Default to B1 if level not found
              frequency: '',
              source: 'oxford',
              pos: pos || ''
            });
            wordsFound++;
          }
        });
        
        if (wordsFound > 0) break; // Stop after finding words with the first successful selector
      }
    }
    
    console.log(`Extracted ${wordsFound} words from page`);
  }

  extractCEFRLevel(text) {
    const match = text.match(/([ABC][12])/i);
    return match ? match[1].toUpperCase() : null;
  }

  detectLevelFromContext($element) {
    // Look for level indicators in parent elements
    const parents = $element.parents();
    
    for (let i = 0; i < parents.length; i++) {
      const parent = parents.eq(i);
      const text = parent.attr('class') + ' ' + parent.attr('id') + ' ' + parent.text();
      const level = this.extractCEFRLevel(text);
      if (level) return level;
    }
    
    return null;
  }

  async useSampleOxfordData() {
    console.log('Using sample Oxford CEFR vocabulary data...');
    
    // Sample words from Oxford learner's dictionary organized by CEFR levels
    const oxfordWords = {
      'A1': [
        { word: 'hello', pos: 'exclamation' },
        { word: 'goodbye', pos: 'exclamation' },
        { word: 'please', pos: 'adverb' },
        { word: 'thank', pos: 'verb' },
        { word: 'sorry', pos: 'adjective' },
        { word: 'yes', pos: 'adverb' },
        { word: 'no', pos: 'adverb' },
        { word: 'good', pos: 'adjective' },
        { word: 'bad', pos: 'adjective' },
        { word: 'big', pos: 'adjective' },
        { word: 'small', pos: 'adjective' },
        { word: 'hot', pos: 'adjective' },
        { word: 'cold', pos: 'adjective' },
        { word: 'old', pos: 'adjective' },
        { word: 'young', pos: 'adjective' },
        { word: 'new', pos: 'adjective' },
        { word: 'happy', pos: 'adjective' },
        { word: 'sad', pos: 'adjective' },
        { word: 'eat', pos: 'verb' },
        { word: 'drink', pos: 'verb' },
        { word: 'sleep', pos: 'verb' },
        { word: 'walk', pos: 'verb' },
        { word: 'run', pos: 'verb' },
        { word: 'read', pos: 'verb' },
        { word: 'write', pos: 'verb' }
      ],
      'A2': [
        { word: 'because', pos: 'conjunction' },
        { word: 'before', pos: 'preposition' },
        { word: 'after', pos: 'preposition' },
        { word: 'during', pos: 'preposition' },
        { word: 'between', pos: 'preposition' },
        { word: 'important', pos: 'adjective' },
        { word: 'interesting', pos: 'adjective' },
        { word: 'difficult', pos: 'adjective' },
        { word: 'easy', pos: 'adjective' },
        { word: 'careful', pos: 'adjective' },
        { word: 'dangerous', pos: 'adjective' },
        { word: 'popular', pos: 'adjective' },
        { word: 'comfortable', pos: 'adjective' },
        { word: 'expensive', pos: 'adjective' },
        { word: 'cheap', pos: 'adjective' },
        { word: 'decide', pos: 'verb' },
        { word: 'choose', pos: 'verb' },
        { word: 'prefer', pos: 'verb' },
        { word: 'suggest', pos: 'verb' },
        { word: 'explain', pos: 'verb' },
        { word: 'describe', pos: 'verb' },
        { word: 'compare', pos: 'verb' },
        { word: 'improve', pos: 'verb' },
        { word: 'practice', pos: 'verb' },
        { word: 'continue', pos: 'verb' }
      ],
      'B1': [
        { word: 'although', pos: 'conjunction' },
        { word: 'however', pos: 'adverb' },
        { word: 'therefore', pos: 'adverb' },
        { word: 'nevertheless', pos: 'adverb' },
        { word: 'furthermore', pos: 'adverb' },
        { word: 'experience', pos: 'noun' },
        { word: 'opportunity', pos: 'noun' },
        { word: 'advantage', pos: 'noun' },
        { word: 'disadvantage', pos: 'noun' },
        { word: 'responsibility', pos: 'noun' },
        { word: 'characteristic', pos: 'noun' },
        { word: 'relationship', pos: 'noun' },
        { word: 'achievement', pos: 'noun' },
        { word: 'development', pos: 'noun' },
        { word: 'improvement', pos: 'noun' },
        { word: 'accomplish', pos: 'verb' },
        { word: 'achieve', pos: 'verb' },
        { word: 'establish', pos: 'verb' },
        { word: 'maintain', pos: 'verb' },
        { word: 'organize', pos: 'verb' },
        { word: 'participate', pos: 'verb' },
        { word: 'recognize', pos: 'verb' },
        { word: 'recommend', pos: 'verb' },
        { word: 'represent', pos: 'verb' },
        { word: 'concentrate', pos: 'verb' }
      ],
      'B2': [
        { word: 'sophisticated', pos: 'adjective' },
        { word: 'comprehensive', pos: 'adjective' },
        { word: 'fundamental', pos: 'adjective' },
        { word: 'significant', pos: 'adjective' },
        { word: 'substantial', pos: 'adjective' },
        { word: 'remarkable', pos: 'adjective' },
        { word: 'outstanding', pos: 'adjective' },
        { word: 'exceptional', pos: 'adjective' },
        { word: 'innovative', pos: 'adjective' },
        { word: 'controversial', pos: 'adjective' },
        { word: 'implementation', pos: 'noun' },
        { word: 'investigation', pos: 'noun' },
        { word: 'methodology', pos: 'noun' },
        { word: 'significance', pos: 'noun' },
        { word: 'consequence', pos: 'noun' },
        { word: 'demonstrate', pos: 'verb' },
        { word: 'investigate', pos: 'verb' },
        { word: 'implement', pos: 'verb' },
        { word: 'evaluate', pos: 'verb' },
        { word: 'analyze', pos: 'verb' },
        { word: 'synthesize', pos: 'verb' },
        { word: 'emphasize', pos: 'verb' },
        { word: 'illustrate', pos: 'verb' },
        { word: 'distinguish', pos: 'verb' },
        { word: 'accommodate', pos: 'verb' }
      ],
      'C1': [
        { word: 'predominantly', pos: 'adverb' },
        { word: 'substantially', pos: 'adverb' },
        { word: 'considerably', pos: 'adverb' },
        { word: 'accordingly', pos: 'adverb' },
        { word: 'consequently', pos: 'adverb' },
        { word: 'intricate', pos: 'adjective' },
        { word: 'elaborate', pos: 'adjective' },
        { word: 'meticulous', pos: 'adjective' },
        { word: 'rigorous', pos: 'adjective' },
        { word: 'astute', pos: 'adjective' },
        { word: 'discretion', pos: 'noun' },
        { word: 'intuition', pos: 'noun' },
        { word: 'resilience', pos: 'noun' },
        { word: 'versatility', pos: 'noun' },
        { word: 'prosperity', pos: 'noun' },
        { word: 'consolidate', pos: 'verb' },
        { word: 'facilitate', pos: 'verb' },
        { word: 'optimize', pos: 'verb' },
        { word: 'rationalize', pos: 'verb' },
        { word: 'substantiate', pos: 'verb' },
        { word: 'corroborate', pos: 'verb' },
        { word: 'vindicate', pos: 'verb' },
        { word: 'perpetuate', pos: 'verb' },
        { word: 'articulate', pos: 'verb' },
        { word: 'elucidate', pos: 'verb' }
      ],
      'C2': [
        { word: 'quintessential', pos: 'adjective' },
        { word: 'ubiquitous', pos: 'adjective' },
        { word: 'pervasive', pos: 'adjective' },
        { word: 'prolific', pos: 'adjective' },
        { word: 'unprecedented', pos: 'adjective' },
        { word: 'exemplary', pos: 'adjective' },
        { word: 'immaculate', pos: 'adjective' },
        { word: 'impeccable', pos: 'adjective' },
        { word: 'fastidious', pos: 'adjective' },
        { word: 'scrupulous', pos: 'adjective' },
        { word: 'perspicacity', pos: 'noun' },
        { word: 'acumen', pos: 'noun' },
        { word: 'sagacity', pos: 'noun' },
        { word: 'discernment', pos: 'noun' },
        { word: 'predilection', pos: 'noun' },
        { word: 'explicate', pos: 'verb' },
        { word: 'expound', pos: 'verb' },
        { word: 'extrapolate', pos: 'verb' },
        { word: 'interpolate', pos: 'verb' },
        { word: 'conceptualize', pos: 'verb' },
        { word: 'epitomize', pos: 'verb' },
        { word: 'encapsulate', pos: 'verb' },
        { word: 'amalgamate', pos: 'verb' },
        { word: 'assimilate', pos: 'verb' },
        { word: 'juxtapose', pos: 'verb' }
      ]
    };

    let frequency = 1;
    for (const [level, words] of Object.entries(oxfordWords)) {
      for (const { word, pos } of words) {
        this.words.push({
          word: word.toLowerCase(),
          cefr_level: level,
          frequency: frequency.toString(),
          source: 'oxford_sample',
          pos: pos
        });
        frequency++;
      }
    }
  }

  async scrapeAll() {
    await fs.ensureDir('./data');
    
    await this.scrapeWordlist();
    
    // Remove duplicates
    const uniqueWords = new Map();
    for (const word of this.words) {
      const key = `${word.word}_${word.cefr_level}`;
      if (!uniqueWords.has(key)) {
        uniqueWords.set(key, word);
      }
    }
    
    this.words = Array.from(uniqueWords.values());
    
    // Write to CSV
    await CSV_WRITER.writeRecords(this.words);
    console.log(`Saved ${this.words.length} words to oxford_words.csv`);
    
    return this.words;
  }
}

// Run the scraper
if (import.meta.url === `file://${process.argv[1]}`) {
  const scraper = new OxfordScraper();
  scraper.scrapeAll()
    .then(() => console.log('Oxford scraping completed'))
    .catch(console.error);
}

export default OxfordScraper;