{"name": "cefr-data-processors", "version": "1.0.0", "description": "Scripts to download and process CEFR word data from various sources", "type": "module", "scripts": {"download-all": "node download-all.js", "process-english-profile": "node english-profile-scraper.js", "process-bnc": "node bnc-processor.js", "process-oxford": "node oxford-scraper.js", "process-kaggle": "node kaggle-downloader.js", "merge-csv": "node csv-merger.js"}, "dependencies": {"axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "puppeteer": "^21.0.0", "fs-extra": "^11.0.0", "path": "^0.12.7"}}