import EnglishProfileScraper from './english-profile-scraper.js';
import BNCProcessor from './bnc-processor.js';
import OxfordScraper from './oxford-scraper.js';
import KaggleDownloader from './kaggle-downloader.js';
import CSVMerger from './csv-merger.js';
import fs from 'fs-extra';

class DataDownloader {
  constructor() {
    this.processors = [
      { name: 'English Profile', class: EnglishProfileScraper },
      { name: 'BNC', class: BNCProcessor },
      { name: 'Oxford', class: OxfordScraper },
      { name: 'Kaggle', class: KaggleDownloader }
    ];
  }

  async downloadAll() {
    console.log('🚀 Starting CEFR data collection from all sources...\n');
    
    // Ensure data directory exists
    await fs.ensureDir('./data');
    
    const results = {};
    
    // Process each source
    for (const processor of this.processors) {
      try {
        console.log(`📊 Processing ${processor.name}...`);
        const instance = new processor.class();
        
        let words;
        if (processor.name === 'BNC') {
          words = await instance.process();
        } else if (processor.name === 'Kaggle') {
          words = await instance.process();
        } else {
          words = await instance.scrapeAll();
        }
        
        results[processor.name] = {
          success: true,
          count: words.length,
          words: words
        };
        
        console.log(`✅ ${processor.name}: ${words.length} words collected\n`);
        
      } catch (error) {
        console.error(`❌ ${processor.name} failed:`, error.message);
        results[processor.name] = {
          success: false,
          error: error.message,
          count: 0
        };
      }
    }
    
    // Merge all CSV files
    console.log('🔄 Merging all data sources...');
    try {
      const merger = new CSVMerger();
      const mergedWords = await merger.merge();
      
      results.merged = {
        success: true,
        count: mergedWords.length,
        file: 'merged_cefr_words.csv'
      };
      
      console.log(`✅ Merged: ${mergedWords.length} unique words\n`);
      
    } catch (error) {
      console.error('❌ Merging failed:', error.message);
      results.merged = {
        success: false,
        error: error.message
      };
    }
    
    // Generate summary report
    await this.generateReport(results);
    
    return results;
  }

  async generateReport(results) {
    console.log('📋 Generating summary report...\n');
    
    const report = {
      timestamp: new Date().toISOString(),
      sources: results,
      summary: {
        totalSources: this.processors.length,
        successfulSources: Object.values(results).filter(r => r.success && r.count > 0).length,
        totalWords: Object.values(results).reduce((sum, r) => sum + (r.count || 0), 0),
        mergedWords: results.merged?.count || 0
      }
    };
    
    // Write detailed report
    await fs.writeJSON('./data/download_report.json', report, { spaces: 2 });
    
    // Print summary
    console.log('='.repeat(50));
    console.log('📊 CEFR DATA COLLECTION SUMMARY');
    console.log('='.repeat(50));
    console.log(`📅 Timestamp: ${new Date().toLocaleString()}`);
    console.log(`🎯 Sources processed: ${report.summary.totalSources}`);
    console.log(`✅ Successful sources: ${report.summary.successfulSources}`);
    console.log(`📝 Total words collected: ${report.summary.totalWords}`);
    console.log(`🔄 Unique merged words: ${report.summary.mergedWords}`);
    console.log('='.repeat(50));
    
    console.log('\n📋 Source Details:');
    for (const [source, data] of Object.entries(results)) {
      const status = data.success ? '✅' : '❌';
      const count = data.count || 0;
      const error = data.error ? ` (${data.error})` : '';
      console.log(`  ${status} ${source}: ${count} words${error}`);
    }
    
    console.log('\n📁 Generated Files:');
    const files = [
      'english_profile_words.csv',
      'bnc_words.csv', 
      'oxford_words.csv',
      'kaggle_cefr_words.csv',
      'merged_cefr_words.csv',
      'merge_statistics.json',
      'download_report.json'
    ];
    
    for (const file of files) {
      const exists = await fs.pathExists(`./data/${file}`);
      const status = exists ? '✅' : '❌';
      console.log(`  ${status} ./data/${file}`);
    }
    
    console.log('\n🎉 Data collection completed!');
    console.log('📖 Run individual processors or merger separately if needed:');
    console.log('   npm run process-english-profile');
    console.log('   npm run process-bnc');
    console.log('   npm run process-oxford');
    console.log('   npm run process-kaggle');
    console.log('   npm run merge-csv');
    
    return report;
  }

  async validateEnvironment() {
    console.log('🔍 Validating environment...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`   Node.js version: ${nodeVersion}`);
    
    // Check if data directory exists
    const dataExists = await fs.pathExists('./data');
    console.log(`   Data directory exists: ${dataExists ? '✅' : '❌'}`);
    
    if (!dataExists) {
      await fs.ensureDir('./data');
      console.log('   ✅ Created data directory');
    }
    
    // Check package.json
    const packageExists = await fs.pathExists('./package.json');
    console.log(`   Package.json exists: ${packageExists ? '✅' : '❌'}`);
    
    if (packageExists) {
      const pkg = await fs.readJSON('./package.json');
      console.log(`   Dependencies: ${Object.keys(pkg.dependencies || {}).length}`);
    }
    
    console.log('✅ Environment validation complete\n');
  }
}

// Main execution
async function main() {
  const downloader = new DataDownloader();
  
  try {
    await downloader.validateEnvironment();
    const results = await downloader.downloadAll();
    
    // Exit with appropriate code
    const hasFailures = Object.values(results).some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default DataDownloader;