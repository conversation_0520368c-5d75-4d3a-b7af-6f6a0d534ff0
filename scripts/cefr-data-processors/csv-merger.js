import fs from 'fs-extra';
import csvParser from 'csv-parser';
import createCsvWriter from 'csv-writer';
import path from 'node:path';

const CSV_WRITER = createCsvWriter.createObjectCsvWriter({
  path: './data/merged_cefr_words.csv',
  header: [
    { id: 'word', title: 'word' },
    { id: 'cefr_level', title: 'cefr_level' },
    { id: 'frequency', title: 'frequency' },
    { id: 'sources', title: 'sources' },
    { id: 'pos', title: 'pos' },
    { id: 'confidence', title: 'confidence' }
  ]
});

class CSVMerger {
  constructor() {
    this.allWords = new Map(); // word -> WordData
    this.sources = [
      'english_profile_words.csv',
      'bnc_words.csv',
      'oxford_words.csv',
      'kaggle_cefr_words.csv'
    ];
  }

  async loadCSVFile(filename) {
    const filepath = path.join('./data', filename);
    
    if (!(await fs.pathExists(filepath))) {
      console.log(`File ${filename} not found, skipping...`);
      return [];
    }

    console.log(`Loading ${filename}...`);
    const words = [];
    
    return new Promise((resolve, reject) => {
      fs.createReadStream(filepath)
        .pipe(csvParser())
        .on('data', (row) => {
          if (row.word && row.word.trim()) {
            words.push({
              word: row.word.toLowerCase().trim(),
              cefr_level: row.cefr_level || '',
              frequency: row.frequency || '',
              source: row.source || filename.replace('_words.csv', ''),
              pos: row.pos || ''
            });
          }
        })
        .on('end', () => {
          console.log(`Loaded ${words.length} words from ${filename}`);
          resolve(words);
        })
        .on('error', reject);
    });
  }

  calculateCEFRConfidence(levels) {
    // Calculate confidence based on agreement between sources
    const levelCounts = {};
    const totalSources = levels.length;
    
    for (const level of levels) {
      levelCounts[level] = (levelCounts[level] || 0) + 1;
    }
    
    const maxCount = Math.max(...Object.values(levelCounts));
    const mostCommonLevel = Object.keys(levelCounts).find(
      level => levelCounts[level] === maxCount
    );
    
    // Confidence is percentage of sources agreeing on the level
    const confidence = (maxCount / totalSources * 100).toFixed(1);
    
    return { level: mostCommonLevel, confidence };
  }

  getCEFRLevelPriority() {
    // Priority order for CEFR levels when merging conflicts
    return {
      'A1': 1, 'A2': 2, 'B1': 3, 'B2': 4, 'C1': 5, 'C2': 6
    };
  }

  getBestFrequency(frequencies) {
    // Get the best frequency value (prefer numeric values)
    const numericFreqs = frequencies
      .filter(f => f && /^\d+$/.test(f))
      .map(f => parseInt(f))
      .sort((a, b) => a - b);
    
    return numericFreqs.length > 0 ? numericFreqs[0].toString() : '';
  }

  getBestPOS(poses) {
    // Get the most detailed POS tag
    const validPoses = poses.filter(pos => pos && pos.trim());
    if (validPoses.length === 0) return '';
    
    // Prefer longer, more specific POS tags
    return validPoses.sort((a, b) => b.length - a.length)[0];
  }

  async mergeWords() {
    console.log('Merging words from all sources...');
    
    // Load all CSV files
    for (const source of this.sources) {
      const words = await this.loadCSVFile(source);
      
      for (const wordData of words) {
        const key = wordData.word;
        
        if (!this.allWords.has(key)) {
          this.allWords.set(key, {
            word: wordData.word,
            levels: [],
            frequencies: [],
            sources: [],
            poses: []
          });
        }
        
        const existing = this.allWords.get(key);
        existing.levels.push(wordData.cefr_level);
        existing.frequencies.push(wordData.frequency);
        existing.sources.push(wordData.source);
        existing.poses.push(wordData.pos);
      }
    }
    
    console.log(`Found ${this.allWords.size} unique words across all sources`);
  }

  async generateMergedCSV() {
    const mergedWords = [];
    const levelPriority = this.getCEFRLevelPriority();
    
    for (const [word, data] of this.allWords.entries()) {
      // Calculate best CEFR level with confidence
      const { level: bestLevel, confidence } = this.calculateCEFRConfidence(data.levels);
      
      // Get best frequency
      const bestFrequency = this.getBestFrequency(data.frequencies);
      
      // Get best POS
      const bestPOS = this.getBestPOS(data.poses);
      
      // Create sources list (unique)
      const uniqueSources = [...new Set(data.sources)].join(';');
      
      mergedWords.push({
        word: word,
        cefr_level: bestLevel || 'B1', // Default to B1 if unknown
        frequency: bestFrequency,
        sources: uniqueSources,
        pos: bestPOS,
        confidence: confidence
      });
    }
    
    // Sort by CEFR level and then by frequency
    mergedWords.sort((a, b) => {
      const levelDiff = levelPriority[a.cefr_level] - levelPriority[b.cefr_level];
      if (levelDiff !== 0) return levelDiff;
      
      const freqA = parseInt(a.frequency) || 999999;
      const freqB = parseInt(b.frequency) || 999999;
      return freqA - freqB;
    });
    
    return mergedWords;
  }

  async generateStatistics(mergedWords) {
    const stats = {
      totalWords: mergedWords.length,
      byLevel: {},
      bySource: {},
      byConfidence: {},
      averageConfidence: 0
    };
    
    let totalConfidence = 0;
    
    for (const word of mergedWords) {
      // Count by level
      stats.byLevel[word.cefr_level] = (stats.byLevel[word.cefr_level] || 0) + 1;
      
      // Count by source combination
      stats.bySource[word.sources] = (stats.bySource[word.sources] || 0) + 1;
      
      // Count by confidence range
      const confRange = word.confidence >= 75 ? 'high' : 
                       word.confidence >= 50 ? 'medium' : 'low';
      stats.byConfidence[confRange] = (stats.byConfidence[confRange] || 0) + 1;
      
      totalConfidence += parseFloat(word.confidence);
    }
    
    stats.averageConfidence = (totalConfidence / mergedWords.length).toFixed(1);
    
    // Write statistics to file
    await fs.writeJSON('./data/merge_statistics.json', stats, { spaces: 2 });
    
    console.log('\n=== MERGE STATISTICS ===');
    console.log(`Total words: ${stats.totalWords}`);
    console.log(`Average confidence: ${stats.averageConfidence}%`);
    console.log('\nWords by CEFR level:');
    for (const [level, count] of Object.entries(stats.byLevel)) {
      console.log(`  ${level}: ${count} words`);
    }
    console.log('\nWords by confidence:');
    for (const [range, count] of Object.entries(stats.byConfidence)) {
      console.log(`  ${range}: ${count} words`);
    }
    
    return stats;
  }

  async merge() {
    await fs.ensureDir('./data');
    
    // Load and merge all words
    await this.mergeWords();
    
    // Generate merged CSV
    const mergedWords = await this.generateMergedCSV();
    
    // Write merged CSV
    await CSV_WRITER.writeRecords(mergedWords);
    console.log(`\nSaved ${mergedWords.length} merged words to merged_cefr_words.csv`);
    
    // Generate statistics
    await this.generateStatistics(mergedWords);
    
    return mergedWords;
  }

  // Quality control methods
  async performQualityChecks() {
    console.log('\n=== QUALITY CHECKS ===');
    
    const mergedFile = './data/merged_cefr_words.csv';
    if (!(await fs.pathExists(mergedFile))) {
      console.log('Merged file not found, run merge first');
      return;
    }
    
    const words = await this.loadCSVFile('merged_cefr_words.csv');
    
    // Check for duplicates
    const duplicates = new Set();
    const seen = new Set();
    for (const word of words) {
      if (seen.has(word.word)) {
        duplicates.add(word.word);
      }
      seen.add(word.word);
    }
    
    console.log(`Duplicates found: ${duplicates.size}`);
    if (duplicates.size > 0) {
      console.log('Duplicate words:', Array.from(duplicates).slice(0, 10).join(', '));
    }
    
    // Check CEFR level distribution
    const levelDist = {};
    for (const word of words) {
      levelDist[word.cefr_level] = (levelDist[word.cefr_level] || 0) + 1;
    }
    
    console.log('\nCEFR Level Distribution:');
    for (const [level, count] of Object.entries(levelDist)) {
      const percentage = (count / words.length * 100).toFixed(1);
      console.log(`  ${level}: ${count} (${percentage}%)`);
    }
    
    // Check for empty or invalid data
    const invalidWords = words.filter(w => 
      !w.word || 
      !w.cefr_level || 
      !/^[ABC][12]$/.test(w.cefr_level) ||
      !w.word.match(/^[a-zA-Z]+$/)
    );
    
    console.log(`Invalid entries found: ${invalidWords.length}`);
    if (invalidWords.length > 0) {
      console.log('Sample invalid entries:', invalidWords.slice(0, 5));
    }
    
    // Check confidence levels
    const lowConfidenceWords = words.filter(w => parseFloat(w.confidence) < 50);
    console.log(`Low confidence words (<50%): ${lowConfidenceWords.length}`);
    
    console.log('\n=== QUALITY CHECK COMPLETE ===');
  }
}

// Run the merger
if (import.meta.url === `file://${process.argv[1]}`) {
  const merger = new CSVMerger();
  
  if (process.argv.includes('--quality-check')) {
    merger.performQualityChecks()
      .then(() => console.log('Quality check completed'))
      .catch(console.error);
  } else {
    merger.merge()
      .then(() => console.log('CSV merging completed'))
      .then(() => merger.performQualityChecks())
      .catch(console.error);
  }
}

export default CSVMerger;