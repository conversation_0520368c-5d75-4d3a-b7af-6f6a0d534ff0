# CEFR Data Processors

Scripts to download and process CEFR word level data from various sources.

## Sources

1. **English Profile**: CEFR vocabulary lists from Cambridge
2. **BNC (British National Corpus)**: Frequency-based word lists  
3. **<PERSON> Learner's Dictionary**: Word lists with CEFR levels
4. **Kaggle CEFR Dataset**: Machine learning dataset for CEFR text prediction

## Setup

```bash
cd scripts/cefr-data-processors
npm install
```

## Usage

### Download all data
```bash
npm run download-all
```

### Individual processors
```bash
npm run process-english-profile
npm run process-bnc
npm run process-oxford
npm run process-kaggle
```

### Merge all CSV files
```bash
npm run merge-csv
```

## Output

All scripts generate CSV files with the following structure:
- `word`: The English word
- `cefr_level`: CEFR level (A1, A2, B1, B2, C1, C2)
- `frequency`: Frequency rank (where available)
- `source`: Data source identifier
- `pos`: Part of speech (where available)

Final merged output: `merged_cefr_words.csv`