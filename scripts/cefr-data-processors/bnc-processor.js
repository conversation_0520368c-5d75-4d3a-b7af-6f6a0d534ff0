import axios from 'axios';
import fs from 'fs-extra';
import createCsvWriter from 'csv-writer';

const CSV_WRITER = createCsvWriter.createObjectCsvWriter({
  path: './data/bnc_words.csv',
  header: [
    { id: 'word', title: 'word' },
    { id: 'cefr_level', title: 'cefr_level' },
    { id: 'frequency', title: 'frequency' },
    { id: 'source', title: 'source' },
    { id: 'pos', title: 'pos' }
  ]
});

class BNCProcessor {
  constructor() {
    this.baseUrl = 'https://www.kilgarriff.co.uk';
    this.words = [];
  }

  // Map frequency ranks to CEFR levels (approximate)
  getCEFRFromFrequency(rank) {
    if (rank <= 1000) return 'A1';
    if (rank <= 2000) return 'A2';
    if (rank <= 5000) return 'B1';
    if (rank <= 10000) return 'B2';
    if (rank <= 20000) return 'C1';
    return 'C2';
  }

  async downloadBNCData() {
    try {
      console.log('Downloading BNC frequency data...');
      
      // Try to download from the main BNC frequency list
      const url = `${this.baseUrl}/bnc-readme.html`;
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        timeout: 15000
      });

      // The actual BNC data might be in a separate file
      // Try common BNC wordlist URLs
      const possibleUrls = [
        `${this.baseUrl}/bnc/lemma.num`,
        `${this.baseUrl}/bnc/word.num`,
        `${this.baseUrl}/BNC/lemma.num`,
        `${this.baseUrl}/BNC/word.num`
      ];

      for (const url of possibleUrls) {
        try {
          console.log(`Trying to download from: ${url}`);
          const dataResponse = await axios.get(url, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            },
            timeout: 10000
          });

          await this.processBNCText(dataResponse.data);
          return true;
        } catch (error) {
          console.log(`Failed to download from ${url}: ${error.message}`);
        }
      }

      throw new Error('Could not download BNC data from any known URL');
      
    } catch (error) {
      console.error('Error downloading BNC data:', error.message);
      console.log('Using sample BNC frequency data...');
      await this.useSampleBNCData();
    }
  }

  async processBNCText(text) {
    console.log('Processing BNC text data...');
    
    const lines = text.split('\n');
    let rank = 1;
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;
      
      // BNC format is usually: frequency word [pos]
      // or: rank frequency word [pos]
      const parts = trimmed.split(/\s+/);
      
      let word, frequency, pos = '';
      
      if (parts.length >= 2) {
        // Try to detect format
        if (/^\d+$/.test(parts[0]) && /^\d+$/.test(parts[1])) {
          // Format: rank frequency word [pos]
          frequency = parseInt(parts[1]);
          word = parts[2];
          pos = parts[3] || '';
        } else if (/^\d+$/.test(parts[0])) {
          // Format: frequency word [pos]
          frequency = parseInt(parts[0]);
          word = parts[1];
          pos = parts[2] || '';
        } else {
          // Format: word frequency [pos]
          word = parts[0];
          frequency = parseInt(parts[1]) || rank;
          pos = parts[2] || '';
        }
        
        if (word && word.match(/^[a-zA-Z]+$/)) {
          this.words.push({
            word: word.toLowerCase(),
            cefr_level: this.getCEFRFromFrequency(rank),
            frequency: rank.toString(),
            source: 'bnc',
            pos: pos
          });
          
          rank++;
          
          // Limit to top 20,000 words to keep file size reasonable
          if (rank > 20000) break;
        }
      }
    }
    
    console.log(`Processed ${this.words.length} words from BNC data`);
  }

  async useSampleBNCData() {
    console.log('Using sample BNC frequency data...');
    
    // Sample high-frequency words from BNC with approximate frequencies
    const sampleWords = [
      // A1 level (top 1000)
      { word: 'the', freq: 1 }, { word: 'of', freq: 2 }, { word: 'and', freq: 3 }, { word: 'a', freq: 4 }, { word: 'to', freq: 5 },
      { word: 'in', freq: 6 }, { word: 'is', freq: 7 }, { word: 'you', freq: 8 }, { word: 'that', freq: 9 }, { word: 'it', freq: 10 },
      { word: 'he', freq: 11 }, { word: 'was', freq: 12 }, { word: 'for', freq: 13 }, { word: 'on', freq: 14 }, { word: 'are', freq: 15 },
      { word: 'as', freq: 16 }, { word: 'with', freq: 17 }, { word: 'his', freq: 18 }, { word: 'they', freq: 19 }, { word: 'i', freq: 20 },
      
      // A2 level (1001-2000)
      { word: 'about', freq: 1100 }, { word: 'who', freq: 1150 }, { word: 'get', freq: 1200 }, { word: 'if', freq: 1250 }, { word: 'go', freq: 1300 },
      { word: 'me', freq: 1350 }, { word: 'when', freq: 1400 }, { word: 'make', freq: 1450 }, { word: 'can', freq: 1500 }, { word: 'like', freq: 1550 },
      
      // B1 level (2001-5000)
      { word: 'time', freq: 2500 }, { word: 'person', freq: 2600 }, { word: 'year', freq: 2700 }, { word: 'way', freq: 2800 }, { word: 'day', freq: 2900 },
      { word: 'thing', freq: 3000 }, { word: 'man', freq: 3100 }, { word: 'world', freq: 3200 }, { word: 'life', freq: 3300 }, { word: 'hand', freq: 3400 },
      
      // B2 level (5001-10000)
      { word: 'system', freq: 6000 }, { word: 'program', freq: 6500 }, { word: 'question', freq: 7000 }, { word: 'government', freq: 7500 }, { word: 'company', freq: 8000 },
      { word: 'number', freq: 8500 }, { word: 'group', freq: 9000 }, { word: 'problem', freq: 9500 }, { word: 'fact', freq: 10000 },
      
      // C1 level (10001-20000)
      { word: 'development', freq: 12000 }, { word: 'information', freq: 13000 }, { word: 'service', freq: 14000 }, { word: 'management', freq: 15000 },
      { word: 'research', freq: 16000 }, { word: 'process', freq: 17000 }, { word: 'policy', freq: 18000 }, { word: 'analysis', freq: 19000 },
      
      // C2 level (20000+)
      { word: 'methodology', freq: 25000 }, { word: 'implementation', freq: 26000 }, { word: 'infrastructure', freq: 27000 }, 
      { word: 'comprehensive', freq: 28000 }, { word: 'contemporary', freq: 29000 }
    ];

    for (const { word, freq } of sampleWords) {
      this.words.push({
        word: word.toLowerCase(),
        cefr_level: this.getCEFRFromFrequency(freq),
        frequency: freq.toString(),
        source: 'bnc_sample',
        pos: ''
      });
    }
  }

  async process() {
    await fs.ensureDir('./data');
    
    await this.downloadBNCData();
    
    // Remove duplicates
    const uniqueWords = new Map();
    for (const word of this.words) {
      if (!uniqueWords.has(word.word)) {
        uniqueWords.set(word.word, word);
      }
    }
    
    this.words = Array.from(uniqueWords.values());
    
    // Sort by frequency
    this.words.sort((a, b) => parseInt(a.frequency) - parseInt(b.frequency));
    
    // Write to CSV
    await CSV_WRITER.writeRecords(this.words);
    console.log(`Saved ${this.words.length} words to bnc_words.csv`);
    
    return this.words;
  }
}

// Run the processor
if (import.meta.url === `file://${process.argv[1]}`) {
  const processor = new BNCProcessor();
  processor.process()
    .then(() => console.log('BNC processing completed'))
    .catch(console.error);
}

export default BNCProcessor;