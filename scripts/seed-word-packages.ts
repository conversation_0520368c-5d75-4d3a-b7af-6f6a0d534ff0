#!/usr/bin/env tsx

import { PrismaClient, Language, Difficulty } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

interface WordPackageData {
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	category: string;
	tags: string[];
	words: Array<{
		term: string;
		language: Language;
	}>;
}

const SAMPLE_WORD_PACKAGES: WordPackageData[] = [
	// English Packages (English words with Vietnamese explanations)
	{
		name: 'Business English Essentials',
		description: 'Essential vocabulary for professional business communication',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Business',
		tags: ['professional', 'workplace', 'communication'],
		words: [
			{ term: 'negotiate', language: Language.EN },
			{ term: 'proposal', language: Language.EN },
			{ term: 'deadline', language: Language.EN },
			{ term: 'revenue', language: Language.EN },
			{ term: 'stakeholder', language: Language.EN },
			{ term: 'strategy', language: Language.EN },
			{ term: 'budget', language: Language.EN },
			{ term: 'meeting', language: Language.EN },
			{ term: 'presentation', language: Language.EN },
			{ term: 'collaboration', language: Language.EN },
		],
	},
	{
		name: 'Travel Vocabulary',
		description: 'Common words and phrases for traveling',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		category: 'Travel',
		tags: ['vacation', 'tourism', 'transportation'],
		words: [
			{ term: 'airport', language: Language.EN },
			{ term: 'passport', language: Language.EN },
			{ term: 'luggage', language: Language.EN },
			{ term: 'hotel', language: Language.EN },
			{ term: 'reservation', language: Language.EN },
			{ term: 'ticket', language: Language.EN },
			{ term: 'destination', language: Language.EN },
			{ term: 'journey', language: Language.EN },
			{ term: 'tourist', language: Language.EN },
			{ term: 'vacation', language: Language.EN },
		],
	},
	{
		name: 'Academic English',
		description: 'Advanced vocabulary for academic writing and research',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		category: 'Academic',
		tags: ['university', 'research', 'writing'],
		words: [
			{ term: 'hypothesis', language: Language.EN },
			{ term: 'methodology', language: Language.EN },
			{ term: 'analysis', language: Language.EN },
			{ term: 'conclusion', language: Language.EN },
			{ term: 'bibliography', language: Language.EN },
			{ term: 'dissertation', language: Language.EN },
			{ term: 'peer-review', language: Language.EN },
			{ term: 'citation', language: Language.EN },
			{ term: 'abstract', language: Language.EN },
			{ term: 'empirical', language: Language.EN },
		],
	},
	{
		name: 'Technology Terms',
		description: 'Modern technology and digital vocabulary',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Technology',
		tags: ['digital', 'software', 'internet'],
		words: [
			{ term: 'algorithm', language: Language.EN },
			{ term: 'database', language: Language.EN },
			{ term: 'interface', language: Language.EN },
			{ term: 'software', language: Language.EN },
			{ term: 'hardware', language: Language.EN },
			{ term: 'network', language: Language.EN },
			{ term: 'cybersecurity', language: Language.EN },
			{ term: 'artificial intelligence', language: Language.EN },
			{ term: 'cloud computing', language: Language.EN },
			{ term: 'blockchain', language: Language.EN },
		],
	},
	// Vietnamese Packages (Vietnamese words with English explanations)
	{
		name: 'Từ vựng Kinh doanh',
		description: 'Từ vựng cần thiết cho giao tiếp kinh doanh chuyên nghiệp',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Kinh doanh',
		tags: ['chuyên nghiệp', 'công việc', 'giao tiếp'],
		words: [
			{ term: 'đàm phán', language: Language.VI },
			{ term: 'đề xuất', language: Language.VI },
			{ term: 'thời hạn', language: Language.VI },
			{ term: 'doanh thu', language: Language.VI },
			{ term: 'bên liên quan', language: Language.VI },
			{ term: 'chiến lược', language: Language.VI },
			{ term: 'ngân sách', language: Language.VI },
			{ term: 'cuộc họp', language: Language.VI },
			{ term: 'thuyết trình', language: Language.VI },
			{ term: 'hợp tác', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Du lịch',
		description: 'Từ vựng thông dụng khi đi du lịch',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.BEGINNER,
		category: 'Du lịch',
		tags: ['nghỉ dưỡng', 'du lịch', 'giao thông'],
		words: [
			{ term: 'sân bay', language: Language.VI },
			{ term: 'hộ chiếu', language: Language.VI },
			{ term: 'hành lý', language: Language.VI },
			{ term: 'khách sạn', language: Language.VI },
			{ term: 'đặt chỗ', language: Language.VI },
			{ term: 'vé', language: Language.VI },
			{ term: 'điểm đến', language: Language.VI },
			{ term: 'hành trình', language: Language.VI },
			{ term: 'khách du lịch', language: Language.VI },
			{ term: 'kỳ nghỉ', language: Language.VI },
		],
	},
	{
		name: 'Từ vựng Công nghệ',
		description: 'Từ vựng về công nghệ hiện đại và số hóa',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Công nghệ',
		tags: ['số hóa', 'phần mềm', 'internet'],
		words: [
			{ term: 'thuật toán', language: Language.VI },
			{ term: 'cơ sở dữ liệu', language: Language.VI },
			{ term: 'giao diện', language: Language.VI },
			{ term: 'phần mềm', language: Language.VI },
			{ term: 'phần cứng', language: Language.VI },
			{ term: 'mạng lưới', language: Language.VI },
			{ term: 'an ninh mạng', language: Language.VI },
			{ term: 'trí tuệ nhân tạo', language: Language.VI },
			{ term: 'điện toán đám mây', language: Language.VI },
			{ term: 'chuỗi khối', language: Language.VI },
		],
	},
];

async function createWordPackage(packageData: WordPackageData): Promise<void> {
	const { words, ...packageInfo } = packageData;

	try {
		// Check if package already exists
		const existingPackage = await prisma.wordPackage.findFirst({
			where: {
				name: packageData.name,
				source_language: packageData.source_language,
				target_language: packageData.target_language,
			},
		});

		if (existingPackage) {
			console.log(`📦 Word package '${packageData.name}' already exists. Skipping...`);
			return;
		}

		// Create the word package
		const wordPackage = await prisma.wordPackage.create({
			data: {
				...packageInfo,
				word_count: words.length,
				is_active: true,
			},
		});

		// Add words to the package
		const wordPackageWords = words.map((word) => ({
			word_package_id: wordPackage.id,
			term: word.term,
			language: word.language,
		}));

		await prisma.wordPackageWord.createMany({
			data: wordPackageWords,
		});

		console.log(`✅ Created word package: ${packageData.name} (${words.length} words)`);
	} catch (error) {
		console.error(`❌ Failed to create word package '${packageData.name}':`, error);
	}
}

async function seedWordPackages(): Promise<void> {
	console.log('🌱 Starting word package seeding...');

	try {
		// Connect to database
		await prisma.$connect();
		console.log('📦 Connected to database');

		// Create word packages
		for (const packageData of SAMPLE_WORD_PACKAGES) {
			await createWordPackage(packageData);
		}

		console.log('\n🎉 Word package seeding completed successfully!');
		console.log(`📊 Created ${SAMPLE_WORD_PACKAGES.length} word packages`);

		// Show summary
		const packageStats = await prisma.wordPackage.groupBy({
			by: ['source_language', 'target_language', 'difficulty'],
			_count: {
				_all: true,
			},
		});

		console.log('\n📋 Package Summary:');
		console.log('==================');
		packageStats.forEach((stat) => {
			console.log(
				`${stat.source_language} → ${stat.target_language} - ${stat.difficulty}: ${stat._count._all} packages`
			);
		});
	} catch (error) {
		console.error('❌ Word package seeding failed:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await seedWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { seedWordPackages };
